import pandas as pd
import numpy as np

# 创建测试数据
test_prompts = [
    "请介绍一下人工智能的发展历史",
    "什么是机器学习？它与深度学习有什么区别？",
    "请解释一下大语言模型的工作原理",
    "Python和Java有什么主要区别？",
    "请用简单的语言解释区块链技术",
    "什么是云计算？它有哪些优势？",
    "请介绍几种常见的数据结构及其应用场景",
    "人工智能在医疗领域有哪些应用？",
    "请解释什么是自然语言处理",
    "量子计算的基本原理是什么？"
]

# 为了测试，复制数据生成更多条目
expanded_prompts = []
for i in range(5):  # 生成50条数据
    for prompt in test_prompts:
        expanded_prompts.append(f"{prompt} (样本 {i+1})")

# 创建DataFrame
df = pd.DataFrame({'A': expanded_prompts})

# 保存为Excel文件
df.to_excel('1.xlsx', index=False)

print(f"已创建测试Excel文件，共 {len(expanded_prompts)} 条数据") 