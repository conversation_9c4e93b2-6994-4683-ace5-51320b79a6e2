import requests
import json

class SimpleVerticalClient:
    """
    简单的Vertical AI客户端
    使用requests库直接发送HTTP请求
    """
    
    def __init__(self, api_key="sk-test-key-123456", base_url="http://localhost:8000"):
        """
        初始化客户端
        
        参数:
            api_key: API密钥
            base_url: API基础URL
        """
        self.api_key = api_key
        self.base_url = base_url
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
    
    def list_models(self):
        """
        获取可用模型列表
        
        返回:
            可用模型列表
        """
        url = f"{self.base_url}/v1/models"
        
        try:
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"获取模型列表时发生错误: {str(e)}")
            return {"error": str(e)}
    
    def chat_completion(self, prompt, model_id="claude-4-sonnet-20250514"):
        """
        发送聊天请求
        
        参数:
            prompt: 用户输入的提示
            model_id: 模型ID
            
        返回:
            模型回复
        """
        url = f"{self.base_url}/v1/chat/completions"
        
        payload = {
            "model": model_id,
            "messages": [{"role": "user", "content": prompt}],
            "stream": False  # 不使用流式响应
        }
        
        try:
            print(f"发送请求到: {url}")
            print(f"请求头: {self.headers}")
            print(f"请求体: {json.dumps(payload, ensure_ascii=False)}")
            
            response = requests.post(url, headers=self.headers, json=payload)
            
            # 打印响应状态和内容
            print(f"响应状态码: {response.status_code}")
            print(f"响应内容: {response.text[:200]}...")  # 只打印前200个字符
            
            response.raise_for_status()
            result = response.json()
            
            # 提取回复内容
            if "choices" in result and len(result["choices"]) > 0:
                return result["choices"][0]["message"]["content"]
            else:
                return f"未能获取有效回复: {result}"
                
        except Exception as e:
            print(f"发送聊天请求时发生错误: {str(e)}")
            return f"发生错误: {str(e)}"

if __name__ == "__main__":
    # 创建客户端
    client = SimpleVerticalClient()
    
    # 获取模型列表
    print("获取可用模型列表...")
    models = client.list_models()
    print(json.dumps(models, indent=2, ensure_ascii=False))
    
    # 可用的模型ID
    available_models = [
        "claude-4-opus-20250514",
        "claude-4-sonnet-20250514"
    ]
    
    # 选择模型
    print("\n可用模型:")
    for i, model_id in enumerate(available_models, 1):
        print(f"{i}. {model_id}")
    
    model_choice = input("\n请选择模型编号 (默认为2, claude-4-sonnet): ")
    
    # 设置默认模型
    selected_model = available_models[1]  # 默认为 claude-4-sonnet
    
    # 处理用户选择
    if model_choice.strip() and model_choice.isdigit():
        choice = int(model_choice)
        if 1 <= choice <= len(available_models):
            selected_model = available_models[choice - 1]
    
    print(f"已选择模型: {selected_model}")
    
    # 与模型聊天
    user_prompt = input("\n请输入您的问题: ")
    print("\n发送聊天请求...")
    
    response = client.chat_completion(user_prompt, selected_model)
    print("\n模型回复:")
    print(response) 