from openai import OpenAI

# 创建OpenAI客户端，指向本地Vertical API服务器
client = OpenAI(
    api_key="sk-test-key-123456",  # 使用您的API密钥
    base_url="http://localhost:8000/v1"  # 指向本地服务器
)

# 可用的模型ID
AVAILABLE_MODELS = [
    "claude-4-opus-20250514",
    "claude-4-sonnet-20250514"
]

def chat_with_vertical(prompt, model_id="claude-4-sonnet-20250514"):
    """
    使用OpenAI库与本地Vertical API聊天
    
    参数:
        prompt (str): 用户输入的提示
        model_id (str): 要使用的模型ID
    
    返回:
        str: 模型的回复
    """
    try:
        # 发送聊天请求
        response = client.chat.completions.create(
            model=model_id,  # 使用指定的模型ID
            messages=[
                {"role": "user", "content": prompt}
            ]
        )
        
        # 提取并返回回复内容
        return response.choices[0].message.content
    
    except Exception as e:
        return f"发生错误: {str(e)}"

def list_available_models():
    """
    列出可用的模型
    
    返回:
        list: 可用模型列表
    """
    try:
        models = client.models.list()
        return models
    
    except Exception as e:
        return f"获取模型列表时发生错误: {str(e)}"

if __name__ == "__main__":
    # 列出可用模型
    print("获取可用模型列表...")
    models = list_available_models()
    print(models)
    
    # 选择模型
    print("\n可用模型:")
    for i, model_id in enumerate(AVAILABLE_MODELS, 1):
        print(f"{i}. {model_id}")
    
    model_choice = input("\n请选择模型编号 (默认为2, claude-4-sonnet): ")
    
    # 设置默认模型
    selected_model = AVAILABLE_MODELS[1]  # 默认为 claude-4-sonnet
    
    # 处理用户选择
    if model_choice.strip() and model_choice.isdigit():
        choice = int(model_choice)
        if 1 <= choice <= len(AVAILABLE_MODELS):
            selected_model = AVAILABLE_MODELS[choice - 1]
    
    print(f"已选择模型: {selected_model}")
    
    # 与模型聊天
    user_prompt = input("\n请输入您的问题: ")
    print("\n发送聊天请求...")
    
    response = chat_with_vertical(user_prompt, selected_model)
    print("\n模型回复:")
    print(response) 