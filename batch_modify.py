import subprocess
import sys

# 定义剩余记录的分类
record_categories = {
    67: "身份/安全验证",
    68: "付款购买商品",
    69: "交易环境",
    70: "身份/安全验证",
    71: "注册登录",
    72: "身份/安全验证",
    73: "发布商品",
    74: "售后(纠纷争议)",
    75: "交易环境",
    76: "注册登录",
    77: "交易环境",
    78: "售后(纠纷争议)",
    79: "售后(纠纷争议)",
    80: "操作交互及UI设计",
    81: "付款购买商品",
    82: "售后(纠纷争议)",
    83: "售后(纠纷争议)",
    84: "交易环境",
    85: "付款购买商品",
    86: "交易环境",
    87: "付款购买商品",
    88: "付款购买商品",
    89: "注册登录",
    90: "售后(纠纷争议)",
    91: "付款购买商品",
    92: "售后(纠纷争议)",
    93: "交易环境",
    94: "付款购买商品",
    95: "注册登录",
    96: "注册登录",
    97: "付款购买商品",
    98: "其他综合问题",
    99: "其他综合问题"
}

def main():
    for index, category in record_categories.items():
        cmd = ["python", "modify_specific_record.py", "-r", str(index), "-c", category]
        print(f"修改记录 {index} 的分类为 '{category}'...")
        
        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            print(result.stdout.strip())
        except subprocess.CalledProcessError as e:
            print(f"修改失败: {e}")
            print(e.stderr)
            
        print("-" * 50)
        
    print("批量修改完成!")

if __name__ == "__main__":
    main() 