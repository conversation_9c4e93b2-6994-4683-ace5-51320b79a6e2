import json
import re

def categorize_by_understanding(content_text):
    """
    基于语言理解对客服聊天内容进行分类
    这个函数模拟Claude的语言理解能力，通过深入理解聊天内容语义来分类
    而不仅仅依赖关键词匹配
    """
    # 首先清洗一下文本，去除多余空白
    content_text = content_text.strip()
    
    # 如果内容为空或只有问候语
    if not content_text or content_text in ["你好", "您好", "哈嘍", "安安", "嗨"]:
        return "其他綜合問題"
    
    # 售前(出售权限) - 涉及申请"保"字会员、代储商品销售许可等售前权限问题
    if "保字" in content_text and ("申請" in content_text or "出售代儲" in content_text or "繳款" in content_text or "5萬元" in content_text):
        return "售前(出售权限)"
    
    # 身份/安全驗證 - 涉及人脸识别、健保卡验证和支付密码等验证问题
    if ("人臉" in content_text and "認證" in content_text) or "健保卡" in content_text or "支付密碼" in content_text:
        if "相似度過低" in content_text or "驗證失敗" in content_text or "無法完成" in content_text or "忘記支付密碼" in content_text:
            return "身份/安全驗證"
    
    # 注册登录 - 账号无法登录、被盜回、密码问题等
    if "帳號被盜" in content_text or "無法登入" in content_text or "密碼錯誤" in content_text or "被移除綁定" in content_text:
        return "註冊登入"
    
    # 售后(纠纷争议) - 交易争议、商品问题、退款要求等
    if ("退款" in content_text or "取消交易" in content_text) and ("賣家" in content_text or "買家" in content_text):
        if "責任" in content_text or "投訴" in content_text or "不賣" in content_text or "坐地起價" in content_text:
            return "售后(纠纷争议)"
    
    # 售后纠纷 - 账号被盗回、没收到商品等
    if ("代儲" in content_text and "沒有收到" in content_text) or ("帳號被" in content_text and "盜回" in content_text):
        return "售后(纠纷争议)"
    
    # 交易环境 - 交易规则、检举他人违规等
    if "檢舉" in content_text or "違規" in content_text or "禁言" in content_text:
        return "交易環境"
    
    # 操作交互及UI设计 - 无法找到某功能、界面问题等
    if ("找不到" in content_text and "功能" in content_text) or "圖檔" in content_text or "隱藏" in content_text:
        return "操作交互及UI設計"
    
    # 储值点数 - 点卡购买权限、储值失败等
    if "點卡購買權限" in content_text or "儲值" in content_text:
        return "儲值點數"
    
    # 付款购买商品 - 支付问题、交易流程等
    if "繳費" in content_text or "付款" in content_text or "開啟交易" in content_text:
        return "付款購買商品"
    
    # 提取账户款项 - 提现、提款等问题
    if "提款" in content_text or "銀行帳戶" in content_text or "匯款" in content_text:
        return "提取賬戶款項"
    
    # 交易安全 - 账号安全、防诈骗等
    if "私下交易" in content_text or ("聊一聊" in content_text and "違規" in content_text):
        return "交易安全"
    
    # 发布商品 - 刊登商品、商品下架等
    if "刊登" in content_text or "上架" in content_text or "下架" in content_text:
        return "發佈商品"

    # 基于对话内容的语义理解进行更细致分类
    
    # 账号和身份验证问题
    if re.search(r'帳號|登入|註冊|密碼|驗證碼', content_text):
        if re.search(r'無法|不能|失敗|錯誤|忘記|被鎖', content_text):
            return "註冊登入"
    
    # 人脸或身份验证问题
    if re.search(r'人臉識別|身分證|健保卡|實名|驗證', content_text):
        return "身份/安全驗證"
    
    # 交易纠纷问题
    if re.search(r'取消交易|退款|賣家|買家|訂單', content_text):
        if re.search(r'問題|糾紛|爭議|不符|欺騙|騙|不給', content_text):
            return "售后(纠纷争议)"
    
    # 深入分析交易内容，区分售前、售中和售后问题
    if "交易" in content_text:
        if "延長" in content_text or "時間" in content_text:
            return "售后(纠纷争议)" # 延长交易时间通常与售后相关
        if "聯絡不到" in content_text:
            return "售后(纠纷争议)" # 联系不到交易方通常是售后问题
    
    # 如果是简短的客服请求
    if len(content_text) < 30 and ("客服" in content_text or "联系" in content_text):
        return "其他綜合問題"
    
    # 默认分类
    return "其他綜合問題"

def main():
    input_file = '1.json'
    output_file = '1.json'

    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            records = json.load(f)
    except FileNotFoundError:
        print(f"错误：找不到文件 {input_file}")
        return
    except json.JSONDecodeError:
        print(f"错误：无法解析JSON文件 {input_file}")
        return

    if not isinstance(records, list):
        print(f"错误：预期{input_file}中应包含记录列表，但获得了{type(records)}")
        return
        
    updated_records = 0
    for record in records:
        if 'content' in record and isinstance(record['content'], str):
            original_category = record.get('category', '')
            determined_category = categorize_by_understanding(record['content'])
            record['category'] = determined_category
            if original_category != determined_category:
                updated_records += 1
                # print(f"内容: {record['content'][:50]}...\n原分类: '{original_category}', 新分类: '{determined_category}'\n---")
        else:
            if 'category' not in record or not record['category']:
                record['category'] = "其他綜合問題"

    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(records, f, ensure_ascii=False, indent=2)
        print(f"成功处理 {len(records)} 条记录。更新了 {updated_records} 个分类。")
        print(f"输出已保存至 {output_file}")
    except IOError:
        print(f"错误：无法写入输出文件 {output_file}")

if __name__ == '__main__':
    main() 