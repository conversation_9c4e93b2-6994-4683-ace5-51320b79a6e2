import json

def modify_category():
    try:
        with open("1.json", "r", encoding="utf-8") as f:
            data = json.load(f)
    except FileNotFoundError:
        print("Error: 1.json not found.")
        return
    except json.JSONDecodeError:
        print("Error: Could not decode JSON from 1.json.")
        return

    if isinstance(data, list) and len(data) > 0:
        if isinstance(data[0], dict):
            data[0]["category"] = "平台问题反馈"
        else:
            print("Error: First element in the JSON array is not an object.")
            return
    else:
        print("Error: JSON data is not a list or is empty.")
        return

    try:
        with open("1.json", "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print("Successfully modified the category of the first record in 1.json to '平台问题反馈'.")
    except IOError:
        print("Error: Could not write the modified data back to 1.json.")

if __name__ == "__main__":
    modify_category() 