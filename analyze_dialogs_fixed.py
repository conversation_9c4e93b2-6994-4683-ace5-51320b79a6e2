import pandas as pd
import json
import re
import os
from typing import List, Dict, Union, Tuple, Optional

# 定义分类规则和关键词
class_rules = [
    {
        "category": "售前(出售权限)",
        "keywords": ["保字", "賣家資格", "開店", "如何成為賣家", "如何銷售", "賣家權限", "開通販售"]
    },
    {
        "category": "售中(交易问题)",
        "keywords": ["訂單狀態", "物流", "延長交易時間", "交易進度", "發貨", "聯繫買家", "聯繫賣家"]
    },
    {
        "category": "售后(纠纷争议)",
        "keywords": ["退款", "退貨", "換貨", "貨不符", "商品有問題", "虛擬物品被找回", "質量問題", "欺詐", "投訴"]
    },
    {
        "category": "註冊登入",
        "keywords": ["無法登入", "密碼錯誤", "忘記密碼", "驗證碼", "註冊失敗", "帳號被鎖", "驗證信"]
    },
    {
        "category": "身份/安全驗證",
        "keywords": ["實名認證", "人臉識別", "證件上傳", "健保卡驗證", "安全手機", "支付密碼", "身分證"]
    },
    {
        "category": "發佈商品",
        "keywords": ["上架", "下架", "編輯商品", "商品審核", "刊登規則", "重複刊登", "商品被刪除"]
    },
    {
        "category": "儲值點數",
        "keywords": ["儲值", "點數未到帳", "充值", "儲值方式", "儲值金額限制", "儲值記錄"]
    },
    {
        "category": "提取賬戶款項",
        "keywords": ["提現", "提款", "提取餘額", "銀行帳戶", "收款帳戶", "手續費", "提現失敗"]
    },
    {
        "category": "付款購買商品",
        "keywords": ["支付方式", "付款失敗", "優惠券", "折扣碼", "訂單金額", "繳費異常", "購買權限"]
    },
    {
        "category": "交易安全",
        "keywords": ["帳號被盜", "密碼被改", "釣魚", "詐騙", "虛假", "贓號", "盜用", "找回", "綁定被移除"]
    },
    {
        "category": "平台穩定性",
        "keywords": ["網站崩潰", "無法打開", "系統卡頓", "功能無響應", "報錯", "加載過慢", "服務器錯誤", "APP閃退"]
    },
    {
        "category": "交易環境",
        "keywords": ["刷單", "虛假瀏覽量", "數據操縱", "惡意低價", "重複刊登", "處罰規則", "停權", "瀏覽量"]
    },
    {
        "category": "操作交互及UI設計",
        "keywords": ["界面", "按鈕位置", "功能找不到", "操作不便", "視覺設計", "使用困難", "自動刷新", "會員中心"]
    },
    {
        "category": "客戶服務",
        "keywords": ["客服", "回覆速度", "服務態度", "解決能力", "專業知識", "等待時間", "客服辛苦了", "感謝客服"]
    },
    {
        "category": "其他綜合問題",
        "keywords": ["發票"]
    }
]

def analyze_dialog(user_messages: List[str]) -> Tuple[List[Dict], Optional[str]]:
    """
    分析对话并返回分类结果
    """
    if not user_messages:
        return [{"category_name": "其他綜合問題", "evidence_snippets": []}], None
    
    combined_message = " ".join(user_messages)
    classifications = []
    
    # 特殊情况：浏览量和交易环境相关
    if "瀏覽" in combined_message and ("洗瀏覽" in combined_message or "數據" in combined_message):
        evidence = [msg for msg in user_messages if "瀏覽" in msg]
        classifications.append({
            "category_name": "交易環境",
            "evidence_snippets": evidence
        })
        suggestion = "建議平台優化浏覽量統計系統，開發更精確的異常浏覽檢測算法，並提供商家查詢日增浏覽詳情的透明功能，以增強信任度。"
        return classifications, suggestion
    
    # 特殊情况：标题价格不符
    if ("標題" in combined_message and "價格" in combined_message) or "不實廣告" in combined_message:
        evidence = [msg for msg in user_messages if "標題" in msg or "不實廣告" in msg]
        classifications.append({
            "category_name": "交易安全",
            "evidence_snippets": evidence
        })
        suggestion = "建議平台強化標題與實際價格一致性審核機制，對於多次違規的賣家增加懲罰力度，並優化舉報流程使買家能更輕鬆提交價格不符證據。"
        return classifications, suggestion
    
    # 特殊情况：身份验证
    if "身分證" in combined_message or "健保卡" in combined_message:
        evidence = [msg for msg in user_messages if "身分證" in msg or "健保卡" in msg]
        classifications.append({
            "category_name": "身份/安全驗證",
            "evidence_snippets": evidence
        })
        return classifications, None
    
    # 特殊情况：客服感谢
    if ("感謝" in combined_message or "謝謝" in combined_message) and "客服" in combined_message:
        evidence = [msg for msg in user_messages if "客服" in msg and ("感謝" in msg or "謝謝" in msg)]
        classifications.append({
            "category_name": "客戶服務",
            "evidence_snippets": evidence
        })
        return classifications, None
    
    # 一般关键词匹配
    for rule in class_rules:
        category = rule["category"]
        evidence = []
        for keyword in rule["keywords"]:
            for msg in user_messages:
                if keyword in msg and msg not in evidence:
                    evidence.append(msg)
        
        if evidence:
            classifications.append({
                "category_name": category,
                "evidence_snippets": evidence
            })
    
    if not classifications:
        return [{"category_name": "其他綜合問題", "evidence_snippets": []}], None
    
    return classifications, None

def main():
    try:
        print("开始读取Excel文件...")
        df = pd.read_excel('处理后文件.xlsx')
        print(f"成功读取Excel文件，共有{len(df)}行数据")
        
        if '對話' in df.columns:
            results = []
            
            # 只处理前3条对话作为测试
            print("处理对话数据...")
            for idx, dialog in enumerate(df['對話'].tolist()[:3]):
                print(f"正在分析第 {idx+1}/3 条对话")
                
                # 提取用户消息
                user_messages = []
                user_mode = False
                current_message = ""
                
                for line in dialog.split('\n'):
                    if "**用戶**" in line:
                        user_mode = True
                        if current_message.strip():
                            user_messages.append(current_message.strip())
                        current_message = ""
                        continue
                        
                    if "**系統回復**" in line or "**客服**" in line:
                        user_mode = False
                        if current_message.strip():
                            user_messages.append(current_message.strip())
                        current_message = ""
                        continue
                        
                    if user_mode and not re.match(r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$', line.strip()):
                        current_message += line + " "
                
                if current_message.strip():
                    user_messages.append(current_message.strip())
                
                # 分析对话
                classifications, suggestion = analyze_dialog(user_messages)
                
                result = {
                    "classifications": classifications,
                    "platform_optimization_suggestion": suggestion
                }
                results.append(result)
            
            # 打印结果
            print("\n分析结果:")
            for idx, result in enumerate(results):
                print(f"\n对话 {idx+1} 的分类:")
                for classification in result["classifications"]:
                    print(f"- {classification['category_name']}")
                    if classification["evidence_snippets"]:
                        print(f"  证据数量: {len(classification['evidence_snippets'])}")
                
                if result["platform_optimization_suggestion"]:
                    print(f"建议: {result['platform_optimization_suggestion']}")
            
            # 保存为简单测试文件
            with open('test_results.json', 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            print("\n测试结果已保存到 test_results.json")
            
        else:
            print("错误：在Excel文件中未找到'對話'列")
    except Exception as e:
        print(f"处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 