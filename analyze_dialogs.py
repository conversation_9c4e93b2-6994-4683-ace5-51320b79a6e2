import pandas as pd
import json
import re
import os
from typing import List, Dict, Union, Tuple, Optional

# 读取Excel文件
try:
    print("开始读取Excel文件...")
    # 确保正确处理中文字符
    df = pd.read_excel('处理后文件.xlsx')
    print(f"成功读取Excel文件，共有{len(df)}行数据")
    
    # 检查是否有"對話"列
    if '對話' in df.columns:
        # 创建结果列表
        results = []
        
        # 分析每条对话
        print(f"开始分析{len(df)}条对话...")
        
        # 定义分类规则和关键词
        class_rules = [
            {
                "category": "售前(出售权限)",
                "keywords": ["保字", "賣家資格", "開店", "如何成為賣家", "如何銷售", "賣家權限", "開通販售", 
                           "保", "賣家", "販售資格", "銷售許可", "開始賣", "申請販售", "販售許可"],
                "patterns": [
                    r"(如何|怎麼|怎样).*?(出售|銷售|賣東西|開店|成為賣家)",
                    r"(想|要|申請).*?(賣家|販售|出售).*?(資格|權限)",
                    r"(如何|怎樣).*?(取得|獲得).*?(保字|賣家資格)",
                    r"(成為|申請).*?(賣家)",
                    r"(開通|開始).*?(販售權限|銷售)"
                ]
            },
            {
                "category": "售中(交易问题)",
                "keywords": ["訂單狀態", "物流", "延長交易時間", "交易進度", "發貨", "聯繫買家", "聯繫賣家", 
                           "交易時間", "訂單進展", "何時到貨", "取貨時間", "交易方式", "尚未發貨", "交易流程"],
                "patterns": [
                    r"(已經付款|已付款|付款後).*?(還沒|尚未|未收到|沒收到)",
                    r"交易(時間|進度|狀態|地點)",
                    r"(延長|加長).*?(交易時間|交易期限)",
                    r"(買家|賣家).*?(聯繫|聯絡).*?(不到|失敗)",
                    r"(什麼時候|何時).*?(到貨|發貨|交付)",
                    r"交易.*?(幾號|什麼時候|如何).*?(完成|結束)"
                ]
            },
            {
                "category": "售后(纠纷争议)",
                "keywords": ["退款", "退貨", "換貨", "貨不符", "商品有問題", "虛擬物品被找回", "質量問題", "欺詐", 
                           "投訴", "惡意評價", "商品損壞", "帳號被收回", "被騙", "不符合描述", "貨品有問題", 
                           "帳號被找回", "商品不符", "差評", "評價問題"],
                "patterns": [
                    r"(收到|拿到|買到).*?(不符合|不一樣|有問題|壞掉|不能用|無法使用)",
                    r"(騙|欺騙|欺詐|詐騙|被騙)",
                    r"(投訴|舉報).*?(賣家|買家)",
                    r"(申請|要求).*?(退款|退貨|換貨)",
                    r"(帳號|賬號|賬戶).*?(被找回|被收回)",
                    r"(惡意|故意).*?(差評|評價)",
                    r"退(款|貨).*?(申請|要求|流程)"
                ]
            },
            {
                "category": "註冊登入",
                "keywords": ["無法登入", "密碼錯誤", "忘記密碼", "驗證碼", "註冊失敗", "帳號被鎖", "驗證信",
                           "登錄問題", "註冊郵箱", "帳號鎖定", "無法註冊", "帳號被凍結", "重置密碼", "密碼重設",
                           "無法登錄", "帳戶無法登入"],
                "patterns": [
                    r"(無法|不能).*?(登入|登錄|註冊|創建帳號)",
                    r"(忘記|忘了|不記得).*?(密碼|帳號)",
                    r"(帳號|賬號).*?(鎖住|鎖定|被鎖|凍結|被封)",
                    r"(密碼|驗證碼).*?(錯誤|不正確|無效|失效)",
                    r"(收不到|未收到|沒收到).*?(驗證碼|驗證信|激活郵件)",
                    r"(如何|怎麼).*?(重置|更改|修改).*?密碼"
                ]
            },
            {
                "category": "身份/安全驗證",
                "keywords": ["實名認證", "人臉識別", "證件上傳", "健保卡驗證", "安全手機", "支付密碼", "二步驗證", 
                           "身分證", "身份證", "證件照", "個人驗證", "身分驗證", "帳戶驗證", "風控驗證", "安全設置"],
                "patterns": [
                    r"(身份|身分).*?(驗證|認證|確認)",
                    r"(上傳|提供|給|傳|拍照).*?(證件|證明|健保卡|身分證|身份證)",
                    r"(安全|付款|支付).*?(設置|設定|驗證|密碼)",
                    r"(二步|兩步|雙重).*?(驗證|認證|保護)",
                    r"(綁定|更換|修改).*?(安全手機|電話|手機號碼)",
                    r"(風控|安全).*?(措施|檢測|程序|操作)"
                ]
            },
            {
                "category": "發佈商品",
                "keywords": ["上架", "下架", "編輯商品", "商品審核", "刊登規則", "重複刊登", "商品被刪除",
                           "商品發佈", "無法刊登", "刊登失敗", "修改商品", "商品信息", "審核不通過", "商品圖片", 
                           "違規刊登"],
                "patterns": [
                    r"(無法|不能|失敗).*?(上架|發佈|刊登).*?(商品|物品|產品|服務)",
                    r"(商品|物品|產品).*?(被刪除|被下架|下架|審核中|審核不過)",
                    r"(刊登|上架|發佈).*?(規則|區域|方式|步驟)",
                    r"(重複|違規|多次).*?(刊登|發佈)",
                    r"(修改|編輯).*?(商品|商品信息|產品信息)",
                    r"(商品|產品|物品).*?(圖片|描述|詳情).*?(上傳|修改|設置)"
                ]
            },
            {
                "category": "儲值點數",
                "keywords": ["儲值", "點數未到帳", "充值", "儲值方式", "儲值金額限制", "儲值記錄", "點數充值",
                           "儲值失敗", "充值渠道", "儲值未成功", "點數不見", "儲值點數", "現金儲值"],
                "patterns": [
                    r"(儲值|充值).*?(未|沒有|失敗|不).*?(到帳|入帳|成功)",
                    r"(如何|怎麼|怎樣).*?(儲值|充值|買點數)",
                    r"(儲值|充值).*?(方式|渠道|記錄|金額|限制|上限)",
                    r"(查詢|確認).*?(儲值|充值).*?(記錄|歷史|明細)",
                    r"(點數|資金|款項).*?(未到|不見|消失)",
                    r"(現金|信用卡|銀行).*?(儲值|充值)"
                ]
            },
            {
                "category": "提取賬戶款項",
                "keywords": ["提現", "提款", "提取餘額", "銀行帳戶", "收款帳戶", "手續費", "提現失敗", "取款", 
                           "提領金額", "提款限制", "收款方式", "提現到帳時間", "提款記錄"],
                "patterns": [
                    r"(如何|怎麼|想要|要).*?(提現|提款|提取|領錢|領款)",
                    r"(提現|提款|取款).*?(失敗|錯誤|問題|延遲|未到賬)",
                    r"(修改|更換|設置).*?(收款帳戶|銀行帳戶|提款賬戶)",
                    r"(提現|提款).*?(手續費|費用|收費)",
                    r"(多久|何時).*?(到賬|入賬)",
                    r"(每天|單筆).*?(提現|提款).*?(上限|限額|限制)"
                ]
            },
            {
                "category": "付款購買商品",
                "keywords": ["支付方式", "付款失敗", "優惠券", "折扣碼", "訂單金額", "繳費異常", "購買權限", 
                           "支付問題", "無法付款", "付款流程", "支付錯誤", "購物車", "下單後", "結算頁面", 
                           "購買點數", "點數購買"],
                "patterns": [
                    r"(無法|不能|失敗).*?(付款|支付|購買|結算|下單)",
                    r"(付款|支付|結算).*?(失敗|錯誤|有問題|無法完成)",
                    r"(購買|買商品).*?(權限|限制|被禁止)",
                    r"(繳費|付款).*?(異常|問題|錯誤)",
                    r"(優惠券|折扣碼).*?(無法使用|無效|過期)",
                    r"(哪些|什麼|如何).*?(支付方式|付款方式)",
                    r"(點數|遊戲點數|儲值點數).*?(購買|買|交易)"
                ]
            },
            {
                "category": "交易安全",
                "keywords": ["帳號被盜", "密碼被改", "釣魚", "詐騙", "虛假", "贓號", "盜用", "找回", "綁定被移除", 
                            "不實廣告", "虛假商品", "騙子", "資料外洩", "個人信息洩露", "信用卡盜刷", "惡意連結", 
                            "私下交易", "盜號", "異地登錄"],
                "patterns": [
                    r"(帳號|賬號|賬戶|賬號|賬戶).*?(被盜|被偷|被改|被黑|遭盜|被盜用)",
                    r"(釣魚|詐騙|騙子|欺騙|被騙|騙人|騙局)",
                    r"(虛假|不實|假冒).*?(廣告|信息|資訊|賣家|商品)",
                    r"(私下|線下).*?(交易|轉賬|匯款)",
                    r"(個人|私人).*?(信息|資料|數據).*?(洩露|外洩|被盜)",
                    r"(異地|不明|陌生).*?(登錄|登入|操作)",
                    r"(遊戲帳號|遊戲賬號).*?(找回|被找回)",
                    r"(可疑|不明|陌生).*?(連結|網站|連接)"
                ]
            },
            {
                "category": "平台穩定性",
                "keywords": ["網站崩潰", "無法打開", "系統卡頓", "功能無響應", "報錯", "加載過慢", "服務器錯誤", 
                           "APP閃退", "頁面無響應", "頁面加載失敗", "伺服器連接失敗", "系統故障", "網頁崩潰", 
                           "白屏", "黑屏", "功能失效"],
                "patterns": [
                    r"(網站|平台|系統|應用|APP).*?(崩潰|卡住|卡頓|無法使用|出錯|故障)",
                    r"(頁面|網頁|界面).*?(打不開|無法打開|加載失敗|無法訪問|載入失敗)",
                    r"(APP|應用).*?(閃退|崩潰|卡死|無反應)",
                    r"(點擊|選擇|操作).*?(沒反應|無響應|沒效果|不起作用)",
                    r"(出現|顯示|報告).*?(錯誤|故障|問題|異常)",
                    r"(功能|操作|服務).*?(無法使用|無法正常工作|失效)"
                ]
            },
            {
                "category": "交易環境",
                "keywords": ["刷單", "虛假瀏覽量", "數據操縱", "惡意低價", "重複刊登", "處罰規則", "停權", "禁言", 
                           "瀏覽量", "市場秩序", "競爭不公平", "平台規則", "違規處罰", "市場公平性", "廣告位", 
                           "洗瀏覽", "推薦位置", "浏览量异常", "賬號限制", "權限調整"],
                "patterns": [
                    r"(刷單|虛假瀏覽|洗瀏覽|操縱數據)",
                    r"(處罰|懲罰|禁言|停權|封號).*?(規則|標準|原因|時間)",
                    r"(重複刊登|惡意低價|價格戰|惡性競爭)",
                    r"(平台|市場).*?(秩序|公平|規則|政策|機制)",
                    r"(瀏覽量|點擊量|數據).*?(不對|不實|造假|異常)",
                    r"(帳號|權限|申訴|舉報).*?(被限制|被管制|被調整)",
                    r"(違反|違背).*?(平台規定|市場規則|使用條款)",
                    r"(賬號|帳號).*?(權限|問與答).*?(被鎖|被限制)"
                ]
            },
            {
                "category": "操作交互及UI設計",
                "keywords": ["界面", "按鈕位置", "功能找不到", "操作不便", "視覺設計", "使用困難", "自動刷新", 
                           "會員中心", "介面設計", "使用體驗", "新版界面", "操作流程", "頁面排版", "功能位置", 
                           "圖示不清楚", "介面不友好"],
                "patterns": [
                    r"(界面|按鈕|功能|圖示|選項).*?(找不到|不好用|不方便|看不清|難找)",
                    r"(操作|使用|瀏覽).*?(困難|麻煩|不便|複雜|不順暢)",
                    r"(無法|不能).*?(自動刷新|重整|刷新頁面)",
                    r"(新版|舊版|當前版本).*?(界面|設計|布局|功能)",
                    r"(會員中心|個人頁面|賣場).*?(設計|佈局|使用體驗)",
                    r"(點擊|查找|搜索).*?(不直觀|很麻煩|困難)"
                ]
            },
            {
                "category": "客戶服務",
                "keywords": ["客服", "回覆速度", "服務態度", "解決能力", "專業知識", "等待時間", "客服辛苦了", 
                           "人工客服", "AI客服", "回應時間", "專員服務", "客服電話", "服務品質", "感謝客服"],
                "patterns": [
                    r"(客服|服務人員|專員).*?(態度|能力|速度|回應|處理|解決)",
                    r"(感謝|謝謝|辛苦).*?(客服|服務|幫助)",
                    r"(客服|客服人員).*?(辛苦|謝謝|感謝|專業)",
                    r"(等待|排隊).*?(時間|太久|很長)",
                    r"(AI|自動|機器人).*?(客服|回覆|服務)",
                    r"(電話|在線|線上).*?(客服|服務)"
                ]
            }
        ]
        
        def analyze_dialog(user_messages: List[str]) -> Tuple[List[Dict[str, Union[str, List[str]]]], Optional[str]]:
            """分析用户消息并返回分类结果"""
            if not user_messages:
                return [{"category_name": "其他綜合問題", "evidence_snippets": []}], None
            
            combined_user_message = " ".join(user_messages).strip()
            
            # 创建分类结果列表
            classifications = []
            suggestions = None
            
            # 根据规则匹配分类
            matched_categories = set()
            
            for rule in class_rules:
                category = rule["category"]
                keyword_match = False
                pattern_match = False
                evidence = []
                
                # 关键词匹配
                for keyword in rule["keywords"]:
                    if keyword in combined_user_message:
                        keyword_match = True
                        # 查找包含关键词的证据
                        for msg in user_messages:
                            if keyword in msg and msg not in evidence:
                                evidence.append(msg)
                
                # 正则表达式匹配
                for pattern in rule["patterns"]:
                    matches = re.search(pattern, combined_user_message)
                    if matches:
                        pattern_match = True
                        # 查找匹配的句子作为证据
                        match_text = matches.group(0)
                        for msg in user_messages:
                            if match_text in msg and msg not in evidence:
                                evidence.append(msg)
                
                if keyword_match or pattern_match:
                    matched_categories.add(category)
                    classifications.append({
                        "category_name": category,
                        "evidence_snippets": evidence
                    })
            
            # 特殊情况处理逻辑
            
            # 特殊情况1：交易环境与浏览量问题
            if "瀏覽" in combined_user_message and ("洗瀏覽" in combined_user_message or "虛假瀏覽" in combined_user_message or "數據" in combined_user_message and "異常" in combined_user_message):
                if not any(c["category_name"] == "交易環境" for c in classifications):
                    evidence = [msg for msg in user_messages if "瀏覽" in msg]
                    classifications.append({
                        "category_name": "交易環境",
                        "evidence_snippets": evidence
                    })
                    matched_categories.add("交易環境")
                suggestions = "建議平台優化浏覽量統計系統，開發更精確的異常浏覽檢測算法，並提供商家查詢日增浏覽詳情的透明功能，以增強信任度。"
            
            # 特殊情况2：标题与价格不符问题
            if ("標題" in combined_user_message and "價格" in combined_user_message) or "不實廣告" in combined_user_message:
                if not any(c["category_name"] == "交易安全" for c in classifications):
                    evidence = [msg for msg in user_messages if ("標題" in msg and "價格" in msg) or "不實廣告" in msg]
                    classifications.append({
                        "category_name": "交易安全",
                        "evidence_snippets": evidence
                    })
                    matched_categories.add("交易安全")
                if not suggestions:
                    suggestions = "建議平台強化標題與實際價格一致性審核機制，對於多次違規的賣家增加懲罰力度，並優化舉報流程使買家能更輕鬆提交價格不符證據。"
            
            # 特殊情况3：身份证、健保卡验证问题 - 通常为安全验证而非注册登入
            if ("身分證" in combined_user_message or "健保卡" in combined_user_message or "證件" in combined_user_message) and not any(c["category_name"] == "身份/安全驗證" for c in classifications):
                evidence = [msg for msg in user_messages if "身分證" in msg or "健保卡" in msg or "證件" in msg]
                if evidence:
                    classifications.append({
                        "category_name": "身份/安全驗證",
                        "evidence_snippets": evidence
                    })
                    matched_categories.add("身份/安全驗證")
            
            # 特殊情况4：发票信息通常为其他综合问题
            if "發票" in combined_user_message and not classifications:
                return [{"category_name": "其他綜合問題", "evidence_snippets": []}], None
            
            # 特殊情况5：客服感谢
            if ("感謝" in combined_user_message or "謝謝" in combined_user_message or "辛苦" in combined_user_message) and "客服" in combined_user_message:
                evidence = [msg for msg in user_messages if ("感謝" in msg or "謝謝" in msg or "辛苦" in msg) and "客服" in msg]
                if evidence and not any(c["category_name"] == "客戶服務" for c in classifications):
                    classifications.append({
                        "category_name": "客戶服務",
                        "evidence_snippets": evidence
                    })
                    matched_categories.add("客戶服務")
            
            # 如果没有匹配到任何分类，则归为"其他綜合問題"
            if not classifications:
                return [{"category_name": "其他綜合問題", "evidence_snippets": []}], None
            
            # 可以定义更多非常规问题的检测和对应建议
            if not suggestions:
                if "操作交互及UI設計" in matched_categories and "自动刷新" in combined_user_message:
                    suggestions = "建議在新版會員中心的賣家掛網通用畫面恢復圖片顯示或提供其他可見元素，以便用戶的瀏覽器刷新插件能夠正常工作，或內建可自定義的自動刷新選項。"
                elif "交易環境" in matched_categories and "瀏覽量" in combined_user_message and "不對" in combined_user_message:
                    suggestions = "建議平台優化浏覽量統計系統，增加防作弊機制，並為商家提供詳細的浏覽數據分析工具，使其能更清楚了解流量來源與分布。"
                elif "售後(糾紛爭議)" in matched_categories and "帳號被找回" in combined_user_message:
                    suggestions = "建議平台加強賣家售賣的遊戲帳號安全檢查機制，要求賣家提供更多安全保證，並對頻繁發生找回問題的賣家進行嚴格審核或限制。"
            
            return classifications, suggestions
        
        for idx, dialog in enumerate(df['對話'].tolist()):
            print(f"正在分析第 {idx+1}/{len(df)} 条对话")
            
            # 提取用户的发言内容（忽略时间戳、客服回复等）
            user_messages = []
            lines = dialog.split('\n')
            user_mode = False
            current_message = ""
            
            for line in lines:
                if "**用戶**" in line:
                    user_mode = True
                    if current_message:  # 如果已经有之前的消息，保存它
                        user_messages.append(current_message.strip())
                    current_message = ""
                    continue
                
                if "**系統回復**" in line or "**客服**" in line:
                    user_mode = False
                    if current_message:
                        user_messages.append(current_message.strip())
                    current_message = ""
                    continue
                
                if user_mode and not re.match(r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$', line.strip()):
                    current_message += line + " "
            
            # 保存最后一个用户消息（如果有）
            if current_message:
                user_messages.append(current_message.strip())
            
            # 分析对话获取分类结果
            classifications, suggestion = analyze_dialog(user_messages)
            
            # 创建符合要求的JSON对象
            result = {
                "classifications": classifications,
                "platform_optimization_suggestion": suggestion
            }
            
            # 添加到结果列表
            results.append(result)
        
        # 将结果保存到JSON文件，确保使用UTF-8编码
        with open('dialog_classifications.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"分析完成! 结果已保存到 dialog_classifications.json")
        print(f"总共处理了 {len(results)} 条对话")
        
        # 生成一个示例文件，确认JSON格式正确
        sample_result = results[0] if results else {"classifications": [{"category_name": "其他綜合問題", "evidence_snippets": []}], "platform_optimization_suggestion": None}
        with open('sample_result.json', 'w', encoding='utf-8') as f:
            json.dump(sample_result, f, ensure_ascii=False, indent=2)
        print("已生成示例文件 sample_result.json")
        
    else:
        print("错误：在Excel文件中未找到'對話'列")
except Exception as e:
    print(f"处理过程中发生错误: {e}")
    import traceback
    traceback.print_exc() 