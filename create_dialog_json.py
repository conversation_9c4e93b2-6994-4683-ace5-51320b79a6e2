import pandas as pd
import json
import re

# 读取Excel文件
print("读取Excel文件...")
df = pd.read_excel('处理后文件.xlsx')
print(f"成功读取，共有{len(df)}行数据")

# 只处理第一个对话作为示例
print("处理第一个对话作为示例...")
dialog = df['對話'].iloc[0]

# 手动构造标准的JSON对象
result = {
    "classifications": [
        {
            "category_name": "交易環境",
            "evidence_snippets": [
                "經過你們的客服與我溝通 是在4/14號晚上9點多致電於我 講了一堆我講重點好了 精查證有被洗瀏覽 我因該是600的瀏覽才是真正的瀏覽人數",
                "經過了一天 4/15號打來跟我確認了有該賣場備喜瀏覽 將幫我還原屬於我真正的瀏覽人數",
                "我敢問經過了一天 我的瀏覽從600 還是跳到600嗎?? 然後我還是推薦第一名"
            ]
        },
        {
            "category_name": "客戶服務",
            "evidence_snippets": [
                "感謝你們客服辛苦了 我只是提出疑問"
            ]
        }
    ],
    "platform_optimization_suggestion": "建議平台優化浏覽量統計系統，開發更精確的異常浏覽檢測算法，並提供商家查詢日增浏覽詳情的透明功能，以增強信任度。"
}

# 保存为JSON文件
with open('dialog_analysis_example.json', 'w', encoding='utf-8') as f:
    json.dump(result, f, ensure_ascii=False, indent=2)

print("示例JSON已保存到 dialog_analysis_example.json")
print("这是JSON的正确格式，供参考")

# 创建一个空的大JSON数组准备填充所有对话分析结果
all_results = []
print("现在您可以使用这个格式分析所有对话，并将结果添加到JSON数组中") 