import json

# 读取处理后的JSON文件
with open('1_categorized.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

# 打印第一条记录的部分内容和分类
print("\n第一条记录示例:")
print(f"内容前50个字符: {data[0]['content'][:50].replace('\n', '\\n')}")
print(f"分类: {data[0]['category']}")

# 统计各分类的数量
print("\n各分类统计:")
categories = {}
for item in data:
    cat = item.get('category', '无分类')
    categories[cat] = categories.get(cat, 0) + 1

# 打印分类统计
for cat, count in categories.items():
    print(f"{cat}: {count}条")

# 打印5个示例记录
print("\n5个分类示例:")
cat_examples = {}
for item in data:
    cat = item.get('category', '无分类')
    if cat not in cat_examples and len(cat_examples) < 5:
        content_preview = item['content'][:50].replace('\n', '\\n') + '...' if len(item['content']) > 50 else item['content'].replace('\n', '\\n')
        cat_examples[cat] = content_preview
        print(f"\n分类: {cat}")
        print(f"内容示例: {content_preview}") 