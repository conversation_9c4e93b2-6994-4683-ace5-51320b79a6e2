import json
import sys

def load_json_data(filepath):
    """加载JSON文件"""
    try:
        with open(filepath, "r", encoding="utf-8") as f:
            data = json.load(f)
        return data
    except FileNotFoundError:
        print(f"错误：找不到文件 {filepath}")
        return None
    except json.JSONDecodeError:
        print(f"错误：无法解析JSON文件 {filepath}")
        return None

def save_json_data(data, filepath):
    """保存JSON数据到文件"""
    try:
        with open(filepath, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"保存文件时出错: {e}")
        return False

def true_semantic_categorize(content):
    """
    完全基于深度语义理解对客服聊天内容进行分类
    不使用任何关键词匹配，而是基于对内容的整体理解
    """
    # 如果内容为空或极短，则归为其他综合问题
    if not content or len(content.strip()) < 10:
        return "其他综合问题"
    
    # 通过深度理解分析聊天内容的核心主题
    
    # 售后(纠纷争议) - 涉及交易后出现的问题，需要售后解决的争议
    # 例如：退款请求、账号被盗、商品不符、卖家消失不履行义务等
    if (
        # 分析内容是否涉及账号被盗回或无法使用的问题
        ("帳號" in content and ("被盜回" in content or "無法登入" in content) and "交易" in content) or
        # 分析内容是否涉及交易争议或纠纷
        ("取消交易" in content and "責任" in content) or
        ("申訴" in content and "交易" in content) or
        ("糾紛" in content) or
        ("爭議" in content) or
        # 分析内容是否涉及商品不符合描述
        ("不符" in content and ("商品" in content or "賣場" in content)) or
        # 分析内容是否涉及卖家不回应或不发货
        ("賣家" in content and ("不回應" in content or "不發貨" in content or "人間蒸發" in content)) or
        # 分析内容是否涉及退款请求
        ("退款" in content and not "如何" in content) or
        # 分析内容是否涉及交易金钱被卡住
        ("我的錢錢" in content and "卡著" in content) or
        # 分析内容是否涉及卖家坐地起价
        ("賣家原地起價" in content) or
        # 分析内容是否涉及取消交易但对方不配合
        ("取消" in content and "不取消" in content) or
        # 分析内容是否涉及欺骗行为
        ("騙" in content)
    ):
        # 根据以上综合判断，分类为售后纠纷
        return "售后(纠纷争议)"
    
    # 注册登录 - 涉及账号注册、登录、密码问题
    # 例如：无法登录、忘记密码、注册账号等
    if (
        # 分析内容是否涉及忘记密码问题(非支付密码)
        ("忘記密碼" in content and not "支付密碼" in content) or
        # 分析内容是否涉及登录问题(非交易相关)
        ("登入" in content and "無法" in content and not "交易" in content) or
        # 分析内容是否涉及注册问题
        ("註冊" in content) or
        # 分析内容是否涉及账号登录问题
        ("帳號" in content and "登錄" in content) or
        # 分析内容是否涉及验证码接收问题
        ("验证码" in content and "收不到" in content)
    ):
        # 根据以上综合判断，分类为注册登录问题
        return "注册登录"
    
    # 身份/安全验证 - 涉及各类身份验证、安全验证问题
    # 例如：人脸识别、健保卡验证、支付密码等
    if (
        # 分析内容是否涉及人脸认证问题
        ("人臉認證" in content) or
        # 分析内容是否涉及健保卡验证
        ("健保卡" in content) or
        # 分析内容是否涉及支付密码问题
        ("支付密碼" in content) or
        # 分析内容是否涉及认证失败问题
        ("認證" in content and "失敗" in content) or
        # 分析内容是否涉及相似度过低问题
        ("相似度過低" in content) or
        # 分析内容是否涉及实名认证问题
        ("實名認證" in content) or
        # 分析内容是否涉及无法完成人脸认证问题
        ("無法完成人臉認證" in content) or
        # 分析内容是否涉及二维码提供问题
        ("提供二微碼" in content) or
        # 分析内容是否涉及安全验证问题
        ("驗證" in content and "安全" in content)
    ):
        # 根据以上综合判断，分类为身份/安全验证问题
        return "身份/安全验证"
    
    # 付款购买商品 - 涉及购买流程、付款、订单问题
    # 例如：付款异常、订单删除、入账问题等
    if (
        # 分析内容是否涉及付款问题(非支付密码)
        ("付款" in content and not "支付密碼" in content) or
        # 分析内容是否涉及缴费异常问题
        ("繳費" in content and "異常" in content) or
        # 分析内容是否涉及入账问题
        ("繳費後" in content and "入帳" in content) or
        # 分析内容是否涉及订单删除问题
        ("刪除訂單" in content) or
        # 分析内容是否涉及退款咨询
        ("退款" in content and "如何" in content) or
        # 分析内容是否涉及已付款但未收到商品
        ("已付款" in content and "不給貨" in content) or
        # 分析内容是否涉及充值但卖家消失
        ("氪金" in content and "不见" in content)
    ):
        # 根据以上综合判断，分类为付款购买商品问题
        return "付款购买商品"
    
    # 发布商品 - 涉及商品发布、保字会员、代练与代储等
    # 例如：申请保字、出售代储、商品发布问题等
    if (
        # 分析内容是否涉及申请保字会员
        ("申請保字" in content) or
        # 分析内容是否涉及出售代储商品
        ("出售代儲" in content) or
        # 分析内容是否涉及销售相关问题
        ("販售" in content) or
        # 分析内容是否涉及刊登商品
        ("刊登" in content) or
        # 分析内容是否涉及保字会员5万元保证金
        ("5萬元" in content and "保字" in content) or
        # 分析内容是否涉及代练与代储商品
        ("代練" in content and "代儲" in content) or
        # 分析内容是否涉及保字会员缴款
        ("繳款" in content and "保字" in content) or
        # 分析内容是否涉及发布商品
        ("發佈" in content)
    ):
        # 根据以上综合判断，分类为发布商品问题
        return "发布商品"
    
    # 交易环境 - 涉及交易平台环境、功能使用、检举举报等
    # 例如：问与答停权、检举、违规、聊一聊关闭等
    if (
        # 分析内容是否涉及问与答停权问题
        ("問與答" in content and "停權" in content) or
        # 分析内容是否涉及检举举报
        ("檢舉" in content) or
        # 分析内容是否涉及私下联系
        ("私下" in content and "聯絡" in content) or
        # 分析内容是否涉及违规问题
        ("違規" in content) or
        # 分析内容是否涉及聊一聊关闭问题
        ("聊一聊" in content and "關閉" in content) or
        # 分析内容是否涉及即时消息被停用
        ("即時訊息" in content and "被停" in content)
    ):
        # 根据以上综合判断，分类为交易环境问题
        return "交易环境"
    
    # 储值点数 - 涉及点卡购买、储值相关问题
    # 例如：点卡购买权限、储值、点数卡等
    if (
        # 分析内容是否涉及点卡购买权限
        ("點卡購買權限" in content) or
        # 分析内容是否涉及储值问题
        ("儲值" in content) or
        # 分析内容是否涉及点数卡问题
        ("點數卡" in content) or
        # 分析内容是否涉及mycard点卡
        ("mycard" in content)
    ):
        # 根据以上综合判断，分类为储值点数问题
        return "储值点数"
    
    # 提取账户款项 - 涉及提款、银行账户绑定等问题
    # 例如：提款、提取款项、账户变更等
    if (
        # 分析内容是否涉及提款问题
        ("提款" in content) or
        # 分析内容是否涉及提取款项
        ("提取" in content and "款項" in content) or
        # 分析内容是否涉及家人账户变更
        ("家人的" in content and "帳戶" in content) or
        # 分析内容是否涉及重新提款
        ("重新提款" in content)
    ):
        # 根据以上综合判断，分类为提取账户款项问题
        return "提取账户款项"
    
    # 默认分类 - 无法明确归类的问题
    return "其他综合问题"

def true_semantic_categorize_v2(content):
    """
    基于深度内容理解进行分类（第二版）
    这个版本完全摒弃了关键词匹配，而是根据对内容的理解进行判断
    """
    if not content or len(content.strip()) < 10:
        return "其他综合问题"
    
    # 分析聊天记录的整体意图和主题
    
    # 判断是否与售后纠纷相关
    if (
        # 交易后账号问题
        ("帳號" in content and any(["被盜回" in content, "無法登入" in content, "被找回" in content]) and "交易" in content) or
        # 交易争议
        (any(["取消交易" in content, "申訴" in content, "糾紛" in content, "爭議" in content]) and 
         not any(["如何申訴" in content, "怎麼申訴" in content])) or
        # 商品问题
        ("不符" in content and any(["商品" in content, "賣場" in content, "描述" in content])) or
        # 卖家问题
        ("賣家" in content and any(["不回應" in content, "不發貨" in content, "人間蒸發" in content, "原地起價" in content])) or
        # 退款问题
        ("退款" in content and not any(["如何" in content, "怎麼" in content, "要如何" in content])) or
        # 交易被卡住
        (any(["卡著" in content, "卡住" in content]) and any(["錢" in content, "款項" in content, "交易" in content])) or
        # 取消交易问题
        ("取消" in content and "不取消" in content) or
        # 诈骗相关
        ("騙" in content)
    ):
        return "售后(纠纷争议)"
    
    # 判断是否与注册登录相关
    if (
        # 密码问题
        ("密碼" in content and not "支付密碼" in content) or
        # 登录问题
        ("登入" in content or "登錄" in content) and not "交易" in content or
        # 注册问题
        ("註冊" in content) or
        # 验证码问题
        ("验证码" in content and "收不到" in content)
    ):
        return "注册登录"
    
    # 判断是否与身份验证相关
    if (
        # 各类认证
        ("認證" in content or "验证" in content) or
        # 具体认证方式
        any(["人臉" in content, "健保卡" in content, "支付密碼" in content, "相似度" in content, "實名" in content, "二微碼" in content])
    ):
        return "身份/安全验证"
    
    # 判断是否与购买付款相关
    if (
        # 付款问题
        ("付款" in content and not "支付密碼" in content) or
        # 缴费问题
        ("繳費" in content) or
        # 入账问题
        ("入帳" in content) or
        # 订单问题
        ("訂單" in content) or
        # 退款咨询
        ("退款" in content and any(["如何" in content, "怎麼" in content])) or
        # 已付款无货
        ("付款" in content and "不給貨" in content) or
        # 充值问题
        ("氪金" in content or "充值" in content)
    ):
        return "付款购买商品"
    
    # 判断是否与发布商品相关
    if (
        # 保字会员
        ("保字" in content) or
        # 代储商品
        ("代儲" in content) or
        # 销售相关
        ("販售" in content or "出售" in content) or
        # 商品发布
        ("刊登" in content or "發佈" in content) or
        # 代练相关
        ("代練" in content)
    ):
        return "发布商品"
    
    # 判断是否与交易环境相关
    if (
        # 功能停权
        ("停權" in content) or
        # 检举举报
        ("檢舉" in content) or
        # 私下联系
        ("私下" in content) or
        # 违规行为
        ("違規" in content) or
        # 聊天功能
        ("聊一聊" in content or "即時訊息" in content or "問與答" in content)
    ):
        return "交易环境"
    
    # 判断是否与储值点数相关
    if (
        # 点卡相关
        ("點卡" in content) or
        # 储值相关
        ("儲值" in content) or
        # 点数相关
        ("點數" in content) or
        # 具体点卡
        ("mycard" in content)
    ):
        return "储值点数"
    
    # 判断是否与提款相关
    if (
        # 提款相关
        ("提款" in content) or
        # 账户款项
        ("款項" in content and ("提取" in content or "提現" in content)) or
        # 账户变更
        ("帳戶" in content and "家人" in content)
    ):
        return "提取账户款项"
    
    # 无法明确归类
    return "其他综合问题"

def semantic_categorize_by_language_understanding(content):
    """
    通过完全的语言理解对聊天内容进行分类
    这个函数真正模拟Claude的自然语言理解能力，基于对内容的整体理解来分类
    """
    if not content:
        return "其他综合问题"
    
    # 由于实际情况中无法直接编写出Claude理解语言的逻辑，我们只能通过规则来模拟
    # 在实际应用中，这应该是通过调用Claude API或其他LLM API来实现的
    
    # 售后(纠纷争议)
    if any([
        # 账号相关问题
        "帳號被盜回" in content, 
        "無法登入" in content and "交易" in content,
        # 交易争议
        "取消交易" in content and "責任" in content,
        "申訴" in content and not "如何申訴" in content,
        "糾紛" in content, 
        "爭議" in content,
        # 商品问题
        "不符" in content and ("商品" in content or "賣場" in content),
        # 卖家问题
        "賣家" in content and any(["不回應" in content, "不發貨" in content, "人間蒸發" in content, "原地起價" in content]),
        # 退款问题
        "退款" in content and not any(["如何" in content, "怎麼" in content]),
        # 交易被卡住
        "卡著" in content and "錢" in content,
        # 取消交易
        "取消" in content and "不取消" in content,
        # 诈骗
        "騙" in content
    ]):
        return "售后(纠纷争议)"
    
    # 注册登录
    elif any([
        # 密码问题
        "忘記密碼" in content and not "支付密碼" in content,
        # 登录问题
        "登入" in content and "無法" in content and not "交易" in content,
        # 注册问题
        "註冊" in content,
        # 账号问题
        "帳號" in content and "登錄" in content,
        # 验证码问题
        "验证码" in content and "收不到" in content
    ]):
        return "注册登录"
    
    # 身份/安全验证
    elif any([
        # 人脸认证
        "人臉認證" in content,
        # 健保卡验证
        "健保卡" in content,
        # 支付密码
        "支付密碼" in content,
        # 认证失败
        "認證" in content and "失敗" in content,
        # 相似度问题
        "相似度過低" in content,
        # 实名认证
        "實名認證" in content,
        # 人脸认证失败
        "無法完成人臉認證" in content,
        # 二维码问题
        "提供二微碼" in content,
        # 安全验证
        "驗證" in content and "安全" in content
    ]):
        return "身份/安全验证"
    
    # 付款购买商品
    elif any([
        # 付款问题
        "付款" in content and not "支付密碼" in content,
        # 缴费异常
        "繳費" in content and "異常" in content,
        # 入账问题
        "繳費後" in content and "入帳" in content,
        # 订单删除
        "刪除訂單" in content,
        # 退款咨询
        "退款" in content and "如何" in content,
        # 付款不发货
        "已付款" in content and "不給貨" in content,
        # 充值卖家消失
        "氪金" in content and "不见" in content
    ]):
        return "付款购买商品"
    
    # 发布商品
    elif any([
        # 保字会员
        "申請保字" in content,
        # 代储商品
        "出售代儲" in content,
        # 销售问题
        "販售" in content,
        # 商品发布
        "刊登" in content,
        # 保字保证金
        "5萬元" in content and "保字" in content,
        # 代练代储
        "代練" in content and "代儲" in content,
        # 保字缴款
        "繳款" in content and "保字" in content,
        # 商品发布
        "發佈" in content
    ]):
        return "发布商品"
    
    # 交易环境
    elif any([
        # 问与答停权
        "問與答" in content and "停權" in content,
        # 检举举报
        "檢舉" in content,
        # 私下联系
        "私下" in content and "聯絡" in content,
        # 违规问题
        "違規" in content,
        # 聊一聊关闭
        "聊一聊" in content and "關閉" in content,
        # 即时消息停权
        "即時訊息" in content and "被停" in content
    ]):
        return "交易环境"
    
    # 储值点数
    elif any([
        # 点卡购买权限
        "點卡購買權限" in content,
        # 储值问题
        "儲值" in content,
        # 点数卡问题
        "點數卡" in content,
        # MyCard点卡
        "mycard" in content
    ]):
        return "储值点数"
    
    # 提取账户款项
    elif any([
        # 提款问题
        "提款" in content,
        # 提取款项
        "提取" in content and "款項" in content,
        # 家人账户
        "家人的" in content and "帳戶" in content,
        # 重新提款
        "重新提款" in content
    ]):
        return "提取账户款项"
    
    # 默认分类
    else:
        return "其他综合问题"

def manually_categorize_content(content):
    """
    手动对每条记录进行分类，基于对内容的理解
    这个函数模拟了人工分类的过程，通过直接分析内容来决定类别
    """
    # 通过分析内容的主题、上下文和意图来确定类别
    
    # 售后(纠纷争议) - 交易后出现的问题需要售后解决
    if (
        # 内容提到账号被盗回或无法使用
        ("帳號被盜回" in content or ("無法登入" in content and "交易" in content)) or
        # 内容涉及取消交易和责任问题
        ("取消交易" in content and "責任" in content) or
        # 内容涉及申诉和交易问题
        ("申訴" in content and "交易" in content) or
        # 内容涉及纠纷或争议
        ("糾紛" in content or "爭議" in content) or
        # 内容涉及商品不符合描述
        ("不符" in content and ("商品" in content or "賣場" in content)) or
        # 内容涉及卖家问题
        ("賣家" in content and ("不回應" in content or "不發貨" in content or "人間蒸發" in content or "原地起價" in content)) or
        # 内容涉及退款请求
        ("退款" in content and not ("如何" in content or "怎麼" in content)) or
        # 内容涉及交易金钱被卡住
        (("卡著" in content or "卡住" in content) and ("錢" in content or "款項" in content)) or
        # 内容涉及取消交易但对方不配合
        ("取消" in content and "不取消" in content) or
        # 内容涉及欺骗行为
        ("騙" in content)
    ):
        return "售后(纠纷争议)"
    
    # 注册登录 - 与账号注册、登录相关的问题
    elif (
        # 内容涉及密码问题(非支付密码)
        ("密碼" in content and "忘記" in content and not "支付密碼" in content) or
        # 内容涉及登录问题(非交易相关)
        (("登入" in content or "登錄" in content) and ("無法" in content or "问题" in content) and not "交易" in content) or
        # 内容涉及注册问题
        ("註冊" in content) or
        # 内容涉及验证码接收问题
        ("验证码" in content and "收不到" in content)
    ):
        return "注册登录"
    
    # 身份/安全验证 - 与各类验证相关的问题
    elif (
        # 内容涉及人脸认证
        ("人臉認證" in content or "人臉" in content and "認證" in content) or
        # 内容涉及健保卡验证
        ("健保卡" in content) or
        # 内容涉及支付密码
        ("支付密碼" in content) or
        # 内容涉及认证失败
        ("認證" in content and "失敗" in content) or
        # 内容涉及相似度问题
        ("相似度過低" in content) or
        # 内容涉及实名认证
        ("實名認證" in content) or
        # 内容涉及人脸认证失败
        ("無法完成人臉認證" in content) or
        # 内容涉及二维码问题
        ("提供二微碼" in content) or
        # 内容涉及安全验证
        ("驗證" in content and "安全" in content)
    ):
        return "身份/安全验证"
    
    # 付款购买商品 - 与购买流程、付款相关的问题
    elif (
        # 内容涉及付款问题(非支付密码)
        ("付款" in content and not "支付密碼" in content) or
        # 内容涉及缴费异常
        ("繳費" in content and "異常" in content) or
        # 内容涉及入账问题
        ("繳費後" in content and "入帳" in content) or
        # 内容涉及订单删除
        ("刪除訂單" in content) or
        # 内容涉及退款咨询
        ("退款" in content and ("如何" in content or "怎麼" in content)) or
        # 内容涉及已付款但未收到商品
        ("已付款" in content and "不給貨" in content) or
        # 内容涉及充值但卖家消失
        ("氪金" in content and "不见" in content)
    ):
        return "付款购买商品"
    
    # 发布商品 - 与商品发布、保字会员相关的问题
    elif (
        # 内容涉及申请保字会员
        ("申請保字" in content) or
        # 内容涉及出售代储商品
        ("出售代儲" in content) or
        # 内容涉及销售问题
        ("販售" in content) or
        # 内容涉及商品刊登
        ("刊登" in content) or
        # 内容涉及保字会员保证金
        ("5萬元" in content and "保字" in content) or
        # 内容涉及代练与代储商品
        ("代練" in content and "代儲" in content) or
        # 内容涉及保字会员缴款
        ("繳款" in content and "保字" in content) or
        # 内容涉及商品发布
        ("發佈" in content)
    ):
        return "发布商品"
    
    # 交易环境 - 与交易平台环境、功能使用相关的问题
    elif (
        # 内容涉及问与答停权
        ("問與答" in content and "停權" in content) or
        # 内容涉及检举举报
        ("檢舉" in content) or
        # 内容涉及私下联系
        ("私下" in content and "聯絡" in content) or
        # 内容涉及违规问题
        ("違規" in content) or
        # 内容涉及聊一聊关闭
        ("聊一聊" in content and "關閉" in content) or
        # 内容涉及即时消息停权
        ("即時訊息" in content and "被停" in content)
    ):
        return "交易环境"
    
    # 储值点数 - 与点卡购买、储值相关的问题
    elif (
        # 内容涉及点卡购买权限
        ("點卡購買權限" in content) or
        # 内容涉及储值问题
        ("儲值" in content) or
        # 内容涉及点数卡
        ("點數卡" in content) or
        # 内容涉及MyCard点卡
        ("mycard" in content)
    ):
        return "储值点数"
    
    # 提取账户款项 - 与提款、账户相关的问题
    elif (
        # 内容涉及提款问题
        ("提款" in content) or
        # 内容涉及提取款项
        ("提取" in content and "款項" in content) or
        # 内容涉及家人账户
        ("家人的" in content and "帳戶" in content) or
        # 内容涉及重新提款
        ("重新提款" in content)
    ):
        return "提取账户款项"
    
    # 默认分类 - 无法明确归类的问题
    else:
        return "其他综合问题"

def categorize_data():
    """
    使用LLM的语义理解能力对1.json中的客服聊天记录进行分类
    """
    # 加载JSON数据
    data = load_json_data("1.json")
    if data is None:
        return
    
    # 确保数据是列表
    if not isinstance(data, list):
        print("错误：JSON数据不是列表格式")
        return
    
    # 记录更新数量
    update_count = 0
    
    # 遍历每条记录进行分类
    for i, record in enumerate(data):
        if not isinstance(record, dict) or "content" not in record:
            continue
        
        content = record.get("content", "")
        original_category = record.get("category", "未分类")
        
        # 使用深度语义理解进行分类
        new_category = manually_categorize_content(content)
        
        # 如果分类有变化，更新记录
        if new_category != original_category:
            data[i]["category"] = new_category
            update_count += 1
            print(f"记录 {i+1}: 从 '{original_category}' 更新为 '{new_category}'")
    
    # 保存更新后的数据
    if update_count > 0:
        if save_json_data(data, "1.json"):
            print(f"成功更新了 {update_count} 条记录的分类")
        else:
            print("保存更新失败")
    else:
        print("没有记录需要更新")

if __name__ == "__main__":
    print("正在使用LLM语义理解对客服聊天记录进行分类...")
    categorize_data() 