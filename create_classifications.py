import json

# 直接创建包含中文的JSON文件
template = {
    "classifications": [
        {
            "category_name": "交易環境",
            "evidence_snippets": [
                "經過你們的客服與我溝通 是在4/14號晚上9點多致電於我 講了一堆我講重點好了 精查證有被洗瀏覽 我因該是600的瀏覽才是真正的瀏覽人數"
            ]
        }
    ],
    "platform_optimization_suggestion": "建議平台優化瀏覽量統計系統，開發更精確的異常瀏覽檢測算法，並提供商家查詢日增瀏覽詳情的透明功能，以增強信任度。"
}

# 保存到文件中
with open('template.json', 'w', encoding='utf-8') as f:
    json.dump(template, f, ensure_ascii=False, indent=2)

print("已创建模板文件 template.json")

# 创建一组分析结果示例
examples = [
    {
        "classifications": [
            {
                "category_name": "交易環境",
                "evidence_snippets": [
                    "經過你們的客服與我溝通 是在4/14號晚上9點多致電於我 講了一堆我講重點好了 精查證有被洗瀏覽 我因該是600的瀏覽才是真正的瀏覽人數",
                    "我敢問經過了一天 我的瀏覽從600 還是跳到600嗎?? 然後我還是推薦第一名"
                ]
            },
            {
                "category_name": "客戶服務",
                "evidence_snippets": [
                    "感謝你們客服辛苦了 我只是提出疑問"
                ]
            }
        ],
        "platform_optimization_suggestion": "建議平台優化瀏覽量統計系統，開發更精確的異常瀏覽檢測算法，並提供商家查詢日增瀏覽詳情的透明功能，以增強信任度。"
    },
    {
        "classifications": [
            {
                "category_name": "身份/安全驗證",
                "evidence_snippets": [
                    "家裡小孩因為要買新道具所以我想取回帳號",
                    "請拍照您的身分證正反面與健保卡彩色圖檔"
                ]
            }
        ],
        "platform_optimization_suggestion": None
    },
    {
        "classifications": [
            {
                "category_name": "其他綜合問題",
                "evidence_snippets": [
                    "我的發票資訊如下：買受人：曹子峯（若未填寫買受人姓名，寄送發票時則默認為會員的註冊姓名）"
                ]
            }
        ],
        "platform_optimization_suggestion": None
    }
]

# 保存示例文件
with open('examples.json', 'w', encoding='utf-8') as f:
    json.dump(examples, f, ensure_ascii=False, indent=2)

print("已创建示例文件 examples.json")

print("\n这些文件使用 UTF-8 编码，包含中文字符。如果你看到乱码，可能是因为编辑器或终端不支持UTF-8显示。")
print("请使用支持UTF-8的工具查看这些JSON文件。")
print("\n按照这个格式，您可以创建包含所有对话分析的 JSON 文件。") 