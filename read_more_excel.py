import pandas as pd

# 读取Excel文件
try:
    df = pd.read_excel('处理后文件.xlsx')
    
    # 打印列名
    print("列名:", list(df.columns))
    
    # 打印总行数
    print(f"总行数: {len(df)}")
    
    # 打印数据的基本统计信息
    print("\n二级分类的统计:")
    print(df['二級分類'].value_counts())
    
    # 打印更多行数据，每次10行
    for i in range(0, 30, 10):
        print(f"\n第{i+1}到第{i+10}行数据:")
        print(df.iloc[i:i+10][['申訴ID', '二級分類', '原話']].to_string())
    
except Exception as e:
    print(f"读取文件时出错: {e}") 