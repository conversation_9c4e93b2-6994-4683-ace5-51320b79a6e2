@echo off
echo 当前正在使用的输入源值可能是15
echo 将依次尝试值0到20，每个值之间间隔5秒
echo 请观察显示器，当显示器切换到其他输入源时，记下当前正在尝试的值
echo.
echo 按任意键开始...
pause > nul

for /l %%i in (0,1,20) do (
    echo.
    echo 正在尝试值 %%i ...
    "%~dp0ControlMyMonitor\ControlMyMonitor.exe" /SetValue Primary 60 %%i
    echo 等待5秒...
    timeout /t 5 > nul
)

echo.
echo 所有值都已尝试完毕
echo 请输入成功切换输入源的值（如果有的话）:
set /p successful_value=

echo.
echo 您输入的值是: %successful_value%
echo 创建快捷切换脚本...

echo @echo off > switch_to_value_%successful_value%.bat
echo echo 正在切换到输入源值 %successful_value% ... >> switch_to_value_%successful_value%.bat
echo "%%~dp0ControlMyMonitor\ControlMyMonitor.exe" /SetValue Primary 60 %successful_value% >> switch_to_value_%successful_value%.bat
echo echo 正在切换第二个显示器... >> switch_to_value_%successful_value%.bat
echo "%%~dp0ControlMyMonitor\ControlMyMonitor.exe" /SetValue "\\.\DISPLAY2\Monitor0" 60 %successful_value% >> switch_to_value_%successful_value%.bat
echo echo 切换完成 >> switch_to_value_%successful_value%.bat
echo pause >> switch_to_value_%successful_value%.bat

echo.
echo 已创建切换脚本: switch_to_value_%successful_value%.bat
echo 您可以双击该文件来切换到这个输入源
pause 