# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations
"""
This file contains the exact signatures for all functions in module
PySide6.QtXml, except for defaults which are replaced by "...".

# mypy: disable-error-code="override, overload-overlap"
"""

# Module `PySide6.QtXml`

import PySide6.QtXml
import PySide6.QtCore

import enum
import typing
from shiboken6 import Shiboken


class QDomAttr(PySide6.QtXml.QDomNode):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, attr: PySide6.QtXml.QDomAttr, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def name(self, /) -> str: ...
    def nodeType(self, /) -> PySide6.QtXml.QDomNode.NodeType: ...
    def ownerElement(self, /) -> PySide6.QtXml.QDomElement: ...
    def setValue(self, value: str, /) -> None: ...
    def specified(self, /) -> bool: ...
    def value(self, /) -> str: ...


class QDomCDATASection(PySide6.QtXml.QDomText):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, cdataSection: PySide6.QtXml.QDomCDATASection, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def nodeType(self, /) -> PySide6.QtXml.QDomNode.NodeType: ...


class QDomCharacterData(PySide6.QtXml.QDomNode):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, characterData: PySide6.QtXml.QDomCharacterData, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def appendData(self, arg: str, /) -> None: ...
    def data(self, /) -> str: ...
    def deleteData(self, offset: int, count: int, /) -> None: ...
    def insertData(self, offset: int, arg: str, /) -> None: ...
    def length(self, /) -> int: ...
    def nodeType(self, /) -> PySide6.QtXml.QDomNode.NodeType: ...
    def replaceData(self, offset: int, count: int, arg: str, /) -> None: ...
    def setData(self, data: str, /) -> None: ...
    def substringData(self, offset: int, count: int, /) -> str: ...


class QDomComment(PySide6.QtXml.QDomCharacterData):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, comment: PySide6.QtXml.QDomComment, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def nodeType(self, /) -> PySide6.QtXml.QDomNode.NodeType: ...


class QDomDocument(PySide6.QtXml.QDomNode):

    class ParseOption(enum.Flag):

        Default                   = ...  # 0x0
        UseNamespaceProcessing    = ...  # 0x1
        PreserveSpacingOnlyNodes  = ...  # 0x2

    class ParseResult(Shiboken.Object):

        @typing.overload
        def __init__(self, /) -> None: ...
        @typing.overload
        def __init__(self, ParseResult: PySide6.QtXml.QDomDocument.ParseResult, /) -> None: ...

        def __copy__(self, /) -> typing.Self: ...


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, document: PySide6.QtXml.QDomDocument, /) -> None: ...
    @typing.overload
    def __init__(self, doctype: PySide6.QtXml.QDomDocumentType, /) -> None: ...
    @typing.overload
    def __init__(self, name: str, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def createAttribute(self, name: str, /) -> PySide6.QtXml.QDomAttr: ...
    def createAttributeNS(self, nsURI: str, qName: str, /) -> PySide6.QtXml.QDomAttr: ...
    def createCDATASection(self, data: str, /) -> PySide6.QtXml.QDomCDATASection: ...
    def createComment(self, data: str, /) -> PySide6.QtXml.QDomComment: ...
    def createDocumentFragment(self, /) -> PySide6.QtXml.QDomDocumentFragment: ...
    def createElement(self, tagName: str, /) -> PySide6.QtXml.QDomElement: ...
    def createElementNS(self, nsURI: str, qName: str, /) -> PySide6.QtXml.QDomElement: ...
    def createEntityReference(self, name: str, /) -> PySide6.QtXml.QDomEntityReference: ...
    def createProcessingInstruction(self, target: str, data: str, /) -> PySide6.QtXml.QDomProcessingInstruction: ...
    def createTextNode(self, data: str, /) -> PySide6.QtXml.QDomText: ...
    def doctype(self, /) -> PySide6.QtXml.QDomDocumentType: ...
    def documentElement(self, /) -> PySide6.QtXml.QDomElement: ...
    def elementById(self, elementId: str, /) -> PySide6.QtXml.QDomElement: ...
    def elementsByTagName(self, tagname: str, /) -> PySide6.QtXml.QDomNodeList: ...
    def elementsByTagNameNS(self, nsURI: str, localName: str, /) -> PySide6.QtXml.QDomNodeList: ...
    def implementation(self, /) -> PySide6.QtXml.QDomImplementation: ...
    def importNode(self, importedNode: PySide6.QtXml.QDomNode, deep: bool, /) -> PySide6.QtXml.QDomNode: ...
    def nodeType(self, /) -> PySide6.QtXml.QDomNode.NodeType: ...
    @typing.overload
    def setContent(self, dev: PySide6.QtCore.QIODevice, /) -> typing.Tuple[bool, str, int, int]: ...
    @typing.overload
    def setContent(self, device: PySide6.QtCore.QIODevice, /, options: PySide6.QtXml.QDomDocument.ParseOption = ...) -> PySide6.QtXml.QDomDocument.ParseResult: ...
    @typing.overload
    def setContent(self, dev: PySide6.QtCore.QIODevice, namespaceProcessing: bool, /) -> typing.Tuple[bool, str, int, int]: ...
    @typing.overload
    def setContent(self, reader: PySide6.QtCore.QXmlStreamReader, /, options: PySide6.QtXml.QDomDocument.ParseOption = ...) -> PySide6.QtXml.QDomDocument.ParseResult: ...
    @typing.overload
    def setContent(self, reader: PySide6.QtCore.QXmlStreamReader, namespaceProcessing: bool, /) -> typing.Tuple[bool, str, int, int]: ...
    @typing.overload
    def setContent(self, text: str, /) -> typing.Tuple[bool, str, int, int]: ...
    @typing.overload
    def setContent(self, data: str, /, options: PySide6.QtXml.QDomDocument.ParseOption = ...) -> PySide6.QtXml.QDomDocument.ParseResult: ...
    @typing.overload
    def setContent(self, text: str, namespaceProcessing: bool, /) -> typing.Tuple[bool, str, int, int]: ...
    @typing.overload
    def setContent(self, text: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, /) -> typing.Tuple[bool, str, int, int]: ...
    @typing.overload
    def setContent(self, text: PySide6.QtCore.QByteArray | bytes | bytearray | memoryview, namespaceProcessing: bool, /) -> typing.Tuple[bool, str, int, int]: ...
    def toByteArray(self, /, indent: int = ...) -> PySide6.QtCore.QByteArray: ...
    def toString(self, /, indent: int = ...) -> str: ...


class QDomDocumentFragment(PySide6.QtXml.QDomNode):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, documentFragment: PySide6.QtXml.QDomDocumentFragment, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def nodeType(self, /) -> PySide6.QtXml.QDomNode.NodeType: ...


class QDomDocumentType(PySide6.QtXml.QDomNode):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, documentType: PySide6.QtXml.QDomDocumentType, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def entities(self, /) -> PySide6.QtXml.QDomNamedNodeMap: ...
    def internalSubset(self, /) -> str: ...
    def name(self, /) -> str: ...
    def nodeType(self, /) -> PySide6.QtXml.QDomNode.NodeType: ...
    def notations(self, /) -> PySide6.QtXml.QDomNamedNodeMap: ...
    def publicId(self, /) -> str: ...
    def systemId(self, /) -> str: ...


class QDomElement(PySide6.QtXml.QDomNode):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, element: PySide6.QtXml.QDomElement, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def attribute(self, name: str, /, defValue: str = ...) -> str: ...
    def attributeNS(self, nsURI: str, localName: str, /, defValue: str = ...) -> str: ...
    def attributeNode(self, name: str, /) -> PySide6.QtXml.QDomAttr: ...
    def attributeNodeNS(self, nsURI: str, localName: str, /) -> PySide6.QtXml.QDomAttr: ...
    def attributes(self, /) -> PySide6.QtXml.QDomNamedNodeMap: ...
    def elementsByTagName(self, tagname: str, /) -> PySide6.QtXml.QDomNodeList: ...
    def elementsByTagNameNS(self, nsURI: str, localName: str, /) -> PySide6.QtXml.QDomNodeList: ...
    def hasAttribute(self, name: str, /) -> bool: ...
    def hasAttributeNS(self, nsURI: str, localName: str, /) -> bool: ...
    def nodeType(self, /) -> PySide6.QtXml.QDomNode.NodeType: ...
    def removeAttribute(self, name: str, /) -> None: ...
    def removeAttributeNS(self, nsURI: str, localName: str, /) -> None: ...
    def removeAttributeNode(self, oldAttr: PySide6.QtXml.QDomAttr, /) -> PySide6.QtXml.QDomAttr: ...
    @typing.overload
    def setAttribute(self, name: str, value: str, /) -> None: ...
    @typing.overload
    def setAttribute(self, name: str, value: int, /) -> None: ...
    @typing.overload
    def setAttribute(self, name: str, value: float, /) -> None: ...
    @typing.overload
    def setAttributeNS(self, nsURI: str, qName: str, value: str, /) -> None: ...
    @typing.overload
    def setAttributeNS(self, nsURI: str, qName: str, value: int, /) -> None: ...
    @typing.overload
    def setAttributeNS(self, nsURI: str, qName: str, value: float, /) -> None: ...
    def setAttributeNode(self, newAttr: PySide6.QtXml.QDomAttr, /) -> PySide6.QtXml.QDomAttr: ...
    def setAttributeNodeNS(self, newAttr: PySide6.QtXml.QDomAttr, /) -> PySide6.QtXml.QDomAttr: ...
    def setTagName(self, name: str, /) -> None: ...
    def tagName(self, /) -> str: ...
    def text(self, /) -> str: ...


class QDomEntity(PySide6.QtXml.QDomNode):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, entity: PySide6.QtXml.QDomEntity, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def nodeType(self, /) -> PySide6.QtXml.QDomNode.NodeType: ...
    def notationName(self, /) -> str: ...
    def publicId(self, /) -> str: ...
    def systemId(self, /) -> str: ...


class QDomEntityReference(PySide6.QtXml.QDomNode):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, entityReference: PySide6.QtXml.QDomEntityReference, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def nodeType(self, /) -> PySide6.QtXml.QDomNode.NodeType: ...


class QDomImplementation(Shiboken.Object):

    class InvalidDataPolicy(enum.Enum):

        AcceptInvalidChars        = ...  # 0x0
        DropInvalidChars          = ...  # 0x1
        ReturnNullNode            = ...  # 0x2


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, implementation: PySide6.QtXml.QDomImplementation, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, other: PySide6.QtXml.QDomImplementation, /) -> bool: ...
    def __ne__(self, other: PySide6.QtXml.QDomImplementation, /) -> bool: ...
    def createDocument(self, nsURI: str, qName: str, doctype: PySide6.QtXml.QDomDocumentType, /) -> PySide6.QtXml.QDomDocument: ...
    def createDocumentType(self, qName: str, publicId: str, systemId: str, /) -> PySide6.QtXml.QDomDocumentType: ...
    def hasFeature(self, feature: str, version: str, /) -> bool: ...
    @staticmethod
    def invalidDataPolicy() -> PySide6.QtXml.QDomImplementation.InvalidDataPolicy: ...
    def isNull(self, /) -> bool: ...
    @staticmethod
    def setInvalidDataPolicy(policy: PySide6.QtXml.QDomImplementation.InvalidDataPolicy, /) -> None: ...


class QDomNamedNodeMap(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, namedNodeMap: PySide6.QtXml.QDomNamedNodeMap, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, other: PySide6.QtXml.QDomNamedNodeMap, /) -> bool: ...
    def __ne__(self, other: PySide6.QtXml.QDomNamedNodeMap, /) -> bool: ...
    def contains(self, name: str, /) -> bool: ...
    def count(self, /) -> int: ...
    def isEmpty(self, /) -> bool: ...
    def item(self, index: int, /) -> PySide6.QtXml.QDomNode: ...
    def length(self, /) -> int: ...
    def namedItem(self, name: str, /) -> PySide6.QtXml.QDomNode: ...
    def namedItemNS(self, nsURI: str, localName: str, /) -> PySide6.QtXml.QDomNode: ...
    def removeNamedItem(self, name: str, /) -> PySide6.QtXml.QDomNode: ...
    def removeNamedItemNS(self, nsURI: str, localName: str, /) -> PySide6.QtXml.QDomNode: ...
    def setNamedItem(self, newNode: PySide6.QtXml.QDomNode, /) -> PySide6.QtXml.QDomNode: ...
    def setNamedItemNS(self, newNode: PySide6.QtXml.QDomNode, /) -> PySide6.QtXml.QDomNode: ...
    def size(self, /) -> int: ...


class QDomNode(Shiboken.Object):

    class EncodingPolicy(enum.Enum):

        EncodingFromDocument      = ...  # 0x1
        EncodingFromTextStream    = ...  # 0x2

    class NodeType(enum.Enum):

        ElementNode               = ...  # 0x1
        AttributeNode             = ...  # 0x2
        TextNode                  = ...  # 0x3
        CDATASectionNode          = ...  # 0x4
        EntityReferenceNode       = ...  # 0x5
        EntityNode                = ...  # 0x6
        ProcessingInstructionNode = ...  # 0x7
        CommentNode               = ...  # 0x8
        DocumentNode              = ...  # 0x9
        DocumentTypeNode          = ...  # 0xa
        DocumentFragmentNode      = ...  # 0xb
        NotationNode              = ...  # 0xc
        BaseNode                  = ...  # 0x15
        CharacterDataNode         = ...  # 0x16


    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, node: PySide6.QtXml.QDomNode, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, other: PySide6.QtXml.QDomNode, /) -> bool: ...
    def __lshift__(self, stream: PySide6.QtCore.QTextStream, /) -> PySide6.QtCore.QTextStream: ...
    def __ne__(self, other: PySide6.QtXml.QDomNode, /) -> bool: ...
    def appendChild(self, newChild: PySide6.QtXml.QDomNode, /) -> PySide6.QtXml.QDomNode: ...
    def attributes(self, /) -> PySide6.QtXml.QDomNamedNodeMap: ...
    def childNodes(self, /) -> PySide6.QtXml.QDomNodeList: ...
    def clear(self, /) -> None: ...
    def cloneNode(self, /, deep: bool = ...) -> PySide6.QtXml.QDomNode: ...
    def columnNumber(self, /) -> int: ...
    def firstChild(self, /) -> PySide6.QtXml.QDomNode: ...
    def firstChildElement(self, /, tagName: str = ..., namespaceURI: str = ...) -> PySide6.QtXml.QDomElement: ...
    def hasAttributes(self, /) -> bool: ...
    def hasChildNodes(self, /) -> bool: ...
    def insertAfter(self, newChild: PySide6.QtXml.QDomNode, refChild: PySide6.QtXml.QDomNode, /) -> PySide6.QtXml.QDomNode: ...
    def insertBefore(self, newChild: PySide6.QtXml.QDomNode, refChild: PySide6.QtXml.QDomNode, /) -> PySide6.QtXml.QDomNode: ...
    def isAttr(self, /) -> bool: ...
    def isCDATASection(self, /) -> bool: ...
    def isCharacterData(self, /) -> bool: ...
    def isComment(self, /) -> bool: ...
    def isDocument(self, /) -> bool: ...
    def isDocumentFragment(self, /) -> bool: ...
    def isDocumentType(self, /) -> bool: ...
    def isElement(self, /) -> bool: ...
    def isEntity(self, /) -> bool: ...
    def isEntityReference(self, /) -> bool: ...
    def isNotation(self, /) -> bool: ...
    def isNull(self, /) -> bool: ...
    def isProcessingInstruction(self, /) -> bool: ...
    def isSupported(self, feature: str, version: str, /) -> bool: ...
    def isText(self, /) -> bool: ...
    def lastChild(self, /) -> PySide6.QtXml.QDomNode: ...
    def lastChildElement(self, /, tagName: str = ..., namespaceURI: str = ...) -> PySide6.QtXml.QDomElement: ...
    def lineNumber(self, /) -> int: ...
    def localName(self, /) -> str: ...
    def namedItem(self, name: str, /) -> PySide6.QtXml.QDomNode: ...
    def namespaceURI(self, /) -> str: ...
    def nextSibling(self, /) -> PySide6.QtXml.QDomNode: ...
    def nextSiblingElement(self, /, taName: str = ..., namespaceURI: str = ...) -> PySide6.QtXml.QDomElement: ...
    def nodeName(self, /) -> str: ...
    def nodeType(self, /) -> PySide6.QtXml.QDomNode.NodeType: ...
    def nodeValue(self, /) -> str: ...
    def normalize(self, /) -> None: ...
    def ownerDocument(self, /) -> PySide6.QtXml.QDomDocument: ...
    def parentNode(self, /) -> PySide6.QtXml.QDomNode: ...
    def prefix(self, /) -> str: ...
    def previousSibling(self, /) -> PySide6.QtXml.QDomNode: ...
    def previousSiblingElement(self, /, tagName: str = ..., namespaceURI: str = ...) -> PySide6.QtXml.QDomElement: ...
    def removeChild(self, oldChild: PySide6.QtXml.QDomNode, /) -> PySide6.QtXml.QDomNode: ...
    def replaceChild(self, newChild: PySide6.QtXml.QDomNode, oldChild: PySide6.QtXml.QDomNode, /) -> PySide6.QtXml.QDomNode: ...
    def save(self, arg__1: PySide6.QtCore.QTextStream, arg__2: int, /, arg__3: PySide6.QtXml.QDomNode.EncodingPolicy = ...) -> None: ...
    def setNodeValue(self, value: str, /) -> None: ...
    def setPrefix(self, pre: str, /) -> None: ...
    def toAttr(self, /) -> PySide6.QtXml.QDomAttr: ...
    def toCDATASection(self, /) -> PySide6.QtXml.QDomCDATASection: ...
    def toCharacterData(self, /) -> PySide6.QtXml.QDomCharacterData: ...
    def toComment(self, /) -> PySide6.QtXml.QDomComment: ...
    def toDocument(self, /) -> PySide6.QtXml.QDomDocument: ...
    def toDocumentFragment(self, /) -> PySide6.QtXml.QDomDocumentFragment: ...
    def toDocumentType(self, /) -> PySide6.QtXml.QDomDocumentType: ...
    def toElement(self, /) -> PySide6.QtXml.QDomElement: ...
    def toEntity(self, /) -> PySide6.QtXml.QDomEntity: ...
    def toEntityReference(self, /) -> PySide6.QtXml.QDomEntityReference: ...
    def toNotation(self, /) -> PySide6.QtXml.QDomNotation: ...
    def toProcessingInstruction(self, /) -> PySide6.QtXml.QDomProcessingInstruction: ...
    def toText(self, /) -> PySide6.QtXml.QDomText: ...


class QDomNodeList(Shiboken.Object):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, nodeList: PySide6.QtXml.QDomNodeList, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def __eq__(self, other: PySide6.QtXml.QDomNodeList, /) -> bool: ...
    def __ne__(self, other: PySide6.QtXml.QDomNodeList, /) -> bool: ...
    def at(self, index: int, /) -> PySide6.QtXml.QDomNode: ...
    def count(self, /) -> int: ...
    def isEmpty(self, /) -> bool: ...
    def item(self, index: int, /) -> PySide6.QtXml.QDomNode: ...
    def length(self, /) -> int: ...
    def size(self, /) -> int: ...


class QDomNotation(PySide6.QtXml.QDomNode):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, notation: PySide6.QtXml.QDomNotation, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def nodeType(self, /) -> PySide6.QtXml.QDomNode.NodeType: ...
    def publicId(self, /) -> str: ...
    def systemId(self, /) -> str: ...


class QDomProcessingInstruction(PySide6.QtXml.QDomNode):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, processingInstruction: PySide6.QtXml.QDomProcessingInstruction, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def data(self, /) -> str: ...
    def nodeType(self, /) -> PySide6.QtXml.QDomNode.NodeType: ...
    def setData(self, data: str, /) -> None: ...
    def target(self, /) -> str: ...


class QDomText(PySide6.QtXml.QDomCharacterData):

    @typing.overload
    def __init__(self, /) -> None: ...
    @typing.overload
    def __init__(self, text: PySide6.QtXml.QDomText, /) -> None: ...

    def __copy__(self, /) -> typing.Self: ...
    def nodeType(self, /) -> PySide6.QtXml.QDomNode.NodeType: ...
    def splitText(self, offset: int, /) -> PySide6.QtXml.QDomText: ...


class QIntList(object): ...


# eof
