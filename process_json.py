import json
import os

# 定义分类判断函数
def categorize_content(content):
    # 各类别的关键词和特征
    categories = {
        "售前(出售权限)": ["成為賣家", "獲取特定商品", "服務的銷售許可", "銷售資格", "保字", "開店流程"],
        "售中(交易问题)": ["訂單處理", "物流進度", "商品交付", "修改未完成訂單", "聯繫交易對方", "延長交易時間"],
        "售后(纠纷争议)": ["商品與描述不符", "服務質量未達承諾", "遊戲帳號被找回", "退款", "退貨", "換貨", "欺詐", "不當行為", "惡意評價", "不履行協議"],
        "註冊登入": ["創建新的帳號", "登入現有帳號", "收不到驗證碼", "忘記帳號", "忘記密碼", "無法登入", "密碼錯誤", "帳號被鎖定"],
        "身份/安全驗證": ["個人身份信息驗證", "實名認證", "人臉識別", "證件上傳", "健保卡驗證", "安全設置", "安全手機", "支付密碼", "二步驗證"],
        "發佈商品": ["創建商品", "編輯商品", "上架", "下架", "管理商品", "刊登規則", "重複刊登", "刊登區域不符", "商品違規"],
        "儲值點數": ["充入資金", "購買平台點數", "儲值後款項未到帳", "儲值方式", "儲值渠道", "儲值金額限制", "儲值記錄", "儲值失敗"],
        "提取賬戶款項": ["可用餘額提取", "提現", "提現申請失敗", "提現處理時長", "手續費", "收款帳戶設置錯誤", "查詢提現記錄"],
        "付款購買商品": ["支付操作", "支付方式", "支付渠道失敗", "優惠券", "折扣碼", "支付頁面錯誤", "購買權限被限制", "訂單金額"],
        "交易安全": ["帳號被盜用", "釣魚鏈接", "詐騙信息", "私下交易", "虛假廣告", "贓號", "交易對手方可信度"]
    }

    # 检查内容中是否包含各类别的关键词
    scores = {}
    for cat, keywords in categories.items():
        score = sum(1 for keyword in keywords if keyword in content)
        scores[cat] = score
    
    # 找出得分最高的类别
    if any(scores.values()):
        max_cat = max(scores, key=scores.get)
        return max_cat
    
    # 如果没有匹配到任何关键词，进行额外判断
    if "未返回至轉出帳戶" in content or "退至您8591帳戶" in content:
        return "提取賬戶款項"
    
    if "訂單" in content and ("退款" in content or "退錢" in content):
        return "售后(纠纷争议)"
        
    if "商品" in content and "購買" in content:
        return "付款購買商品"
        
    # 默认返回
    return "售中(交易问题)"

try:
    # 读取JSON文件
    with open('1.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 输出文件基本信息
    print(f'文件结构类型: {type(data)}')
    if isinstance(data, list):
        print(f'数据条目数: {len(data)}')
    else:
        print('文件不是列表结构')
        exit(1)
    
    # 示例显示前两条内容
    print('\n前两条内容示例:')
    for item in data[:2]:
        print(json.dumps(item, ensure_ascii=False)[:200] + '...' if len(json.dumps(item, ensure_ascii=False)) > 200 else json.dumps(item, ensure_ascii=False))
    
    # 计数器
    empty_category_count = 0
    filled_category_count = 0
    
    # 遍历并填充category字段
    for item in data:
        if "content" in item and (not item.get("category") or item["category"] == ""):
            content = item["content"]
            item["category"] = categorize_content(content)
            empty_category_count += 1
        elif "category" in item and item["category"]:
            filled_category_count += 1
    
    print(f"\n总共填充了 {empty_category_count} 条空白分类记录")
    print(f"已有 {filled_category_count} 条记录有分类")
    
    # 保存更新后的JSON
    with open('1_categorized.json', 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    
    print("\n已保存更新后的文件到 1_categorized.json")
    
    # 输出部分填充后的内容作为示例
    print("\n填充后的示例:")
    for item in data[:5]:
        if "content" in item and "category" in item:
            content_preview = item["content"][:50].replace("\n", "\\n") + "..." if len(item["content"]) > 50 else item["content"].replace("\n", "\\n")
            print(f"内容: {content_preview}")
            print(f"分类: {item['category']}\n")

except Exception as e:
    print(f"处理过程中出错: {str(e)}") 