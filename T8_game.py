import requests
import pandas as pd
from bs4 import BeautifulSoup
import time
import re

def get_game_info(page=1):
    """
    获取游戏列表信息
    """
    url = f"https://admin.8591.com.tw/admin.php?page={page}&module=gameList&action=list"
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Referer": f"https://admin.8591.com.tw/admin.php?page={page-1 if page > 1 else 1}&module=gameList&action=list",
        "Cookie": "webp=1; PHPSESSID=366a5ee0451026946756555fa6815c416aa8ca3e; leftcookie=showleft01-showleft11-showleft21-showleft31-showleft41-showleft51-showleft61-showleft71-showleft81-showleft91-showleft101-showleft111-showleft121-showleft131-;"
    }
    
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        return response.text
    except requests.RequestException as e:
        print(f"请求错误: {e}")
        return None

def parse_game_info(html):
    """
    解析HTML获取游戏信息
    """
    if not html:
        return []
    
    soup = BeautifulSoup(html, 'html.parser')
    game_data = []
    
    # 查找所有游戏行
    rows = soup.select('tr.menubg, tr.menubg2')
    
    for row in rows:
        # 获取所有单元格
        cells = row.find_all('td')
        if len(cells) < 7:
            continue
            
        # 获取编号
        game_id = cells[0].text.strip()
        
        # 获取游戏名称(二级分类)
        game_name_link = cells[1].find('a')
        game_name = game_name_link.text.strip() if game_name_link else "未知"
        
        # 获取游戏类型
        game_type_input = row.select_one('input[name^="setType"]')
        game_type = game_type_input['value'] if game_type_input else "未知"
        
        # 确保数据有效
        if game_id and game_name != "未知":
            game_data.append({
                '编号': game_id,
                '二级分类': game_name,
                '游戏类型': game_type
            })
    
    return game_data

def has_more_data(html):
    """
    检查页面是否有数据，用于判断是否继续翻页
    """
    if not html:
        return False
    
    soup = BeautifulSoup(html, 'html.parser')
    rows = soup.select('tr.menubg, tr.menubg2')
    return len(rows) > 0

def main():
    all_game_data = []
    
    # 已知总页数为49页
    total_pages = 49
    print(f"将获取全部 {total_pages} 页数据")
    
    # 处理所有页面
    for page in range(1, total_pages + 1):
        print(f"正在处理第 {page}/{total_pages} 页...")
        html = get_game_info(page)
        
        if not html:
            print(f"第 {page} 页请求失败，尝试重试...")
            # 重试一次
            time.sleep(2)
            html = get_game_info(page)
            if not html:
                print(f"第 {page} 页请求失败，跳过此页")
                continue
        
        if not has_more_data(html):
            print(f"第 {page} 页没有数据，可能已到达末页")
            break
            
        game_data = parse_game_info(html)
        if game_data:
            all_game_data.extend(game_data)
            print(f"第 {page} 页: 获取到 {len(game_data)} 条数据")
        else:
            print(f"第 {page} 页: 未获取到数据")
        
        # 添加延迟，避免请求过快被限制
        time.sleep(1.5)
    
    # 创建DataFrame并保存到Excel
    if all_game_data:
        df = pd.DataFrame(all_game_data)
        excel_file = "T8_game_full.xlsx"
        df.to_excel(excel_file, index=False)
        print(f"共获取 {len(all_game_data)} 条数据，已保存到 {excel_file}")
    else:
        print("没有获取到数据")

if __name__ == "__main__":
    main() 