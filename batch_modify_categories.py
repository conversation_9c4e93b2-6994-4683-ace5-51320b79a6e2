import json
import re
import argparse
from collections import defaultdict

def load_json_data(filepath):
    """加载JSON文件"""
    try:
        with open(filepath, "r", encoding="utf-8") as f:
            data = json.load(f)
        return data
    except FileNotFoundError:
        print(f"错误：找不到文件 {filepath}")
        return None
    except json.JSONDecodeError:
        print(f"错误：无法解析JSON文件 {filepath}")
        return None

def save_json_data(data, filepath):
    """保存JSON数据到文件"""
    try:
        with open(filepath, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"保存文件时出错: {e}")
        return False

def modify_categories_by_keyword(data, keywords, new_category, case_sensitive=False):
    """根据关键词修改记录的类别
    
    Args:
        data (list): JSON记录列表
        keywords (list): 关键词列表，符合其中任一关键词的记录都将被修改
        new_category (str): 新的类别名称
        case_sensitive (bool): 是否区分大小写
    
    Returns:
        tuple: (修改的记录数, 修改后的数据)
    """
    modified_count = 0
    modified_indices = []
    
    for i, record in enumerate(data):
        content = record.get('content', '')
        
        if not case_sensitive:
            content_to_match = content.lower()
            keywords_to_match = [k.lower() for k in keywords]
        else:
            content_to_match = content
            keywords_to_match = keywords
        
        if any(keyword in content_to_match for keyword in keywords_to_match):
            original_category = record.get('category', '未分类')
            if original_category != new_category:
                record['category'] = new_category
                modified_count += 1
                modified_indices.append(i)
    
    return modified_count, data, modified_indices

def modify_categories_by_regex(data, pattern, new_category, case_sensitive=True):
    """根据正则表达式修改记录的类别
    
    Args:
        data (list): JSON记录列表
        pattern (str): 正则表达式模式
        new_category (str): 新的类别名称
        case_sensitive (bool): 是否区分大小写
    
    Returns:
        tuple: (修改的记录数, 修改后的数据)
    """
    modified_count = 0
    modified_indices = []
    
    flags = 0 if case_sensitive else re.IGNORECASE
    compiled_pattern = re.compile(pattern, flags)
    
    for i, record in enumerate(data):
        content = record.get('content', '')
        
        if compiled_pattern.search(content):
            original_category = record.get('category', '未分类')
            if original_category != new_category:
                record['category'] = new_category
                modified_count += 1
                modified_indices.append(i)
    
    return modified_count, data, modified_indices

def show_modified_records(data, indices, limit=5):
    """显示已修改的记录信息"""
    print(f"\n修改的记录示例 (前{min(limit, len(indices))}条):")
    for i, idx in enumerate(indices[:limit]):
        record = data[idx]
        content_sample = record.get('content', '')[:100] + '...' if len(record.get('content', '')) > 100 else record.get('content', '')
        print(f"{i+1}. 索引 {idx}: [类别 = {record.get('category', '未分类')}]")
        print(f"   内容预览: {content_sample}\n")

def count_categories(data):
    """统计各个类别的记录数量"""
    category_counts = defaultdict(int)
    for record in data:
        category = record.get('category', '未分类')
        category_counts[category] += 1
    
    return dict(sorted(category_counts.items(), key=lambda x: x[1], reverse=True))

def main():
    parser = argparse.ArgumentParser(description="批量修改JSON记录的类别")
    parser.add_argument("--input", "-i", default="1.json", help="输入JSON文件路径")
    parser.add_argument("--output", "-o", default=None, help="输出JSON文件路径 (默认覆盖输入文件)")
    parser.add_argument("--keywords", "-k", nargs='+', help="用于匹配的关键词列表")
    parser.add_argument("--regex", "-r", help="用于匹配的正则表达式")
    parser.add_argument("--category", "-c", required=True, help="要设置的新类别")
    parser.add_argument("--ignore-case", "-ic", action="store_true", help="忽略大小写")
    parser.add_argument("--statistics", "-s", action="store_true", help="显示修改前后的类别统计")
    parser.add_argument("--dry-run", "-d", action="store_true", help="仅模拟执行，不实际修改文件")
    
    args = parser.parse_args()
    
    # 设置输出文件路径
    output_file = args.output if args.output else args.input
    
    # 加载数据
    data = load_json_data(args.input)
    if not data:
        return
    
    print(f"已加载 {len(data)} 条记录")
    
    if args.statistics:
        before_stats = count_categories(data)
        print("\n修改前的类别统计:")
        for category, count in before_stats.items():
            print(f"- {category}: {count}条")
    
    # 根据匹配方式修改类别
    if args.regex:
        print(f"\n使用正则表达式 '{args.regex}' 匹配记录，将类别设置为 '{args.category}'")
        modified_count, updated_data, modified_indices = modify_categories_by_regex(
            data, args.regex, args.category, not args.ignore_case
        )
    elif args.keywords:
        print(f"\n使用关键词 {args.keywords} 匹配记录，将类别设置为 '{args.category}'")
        modified_count, updated_data, modified_indices = modify_categories_by_keyword(
            data, args.keywords, args.category, not args.ignore_case
        )
    else:
        print("错误: 必须提供关键词(--keywords)或正则表达式(--regex)参数")
        return
    
    print(f"\n找到 {modified_count} 条匹配记录")
    
    if modified_count > 0:
        show_modified_records(updated_data, modified_indices)
    
    if args.statistics:
        after_stats = count_categories(updated_data)
        print("\n修改后的类别统计:")
        for category, count in after_stats.items():
            print(f"- {category}: {count}条")
    
    if not args.dry_run and modified_count > 0:
        if save_json_data(updated_data, output_file):
            print(f"\n成功修改 {modified_count} 条记录，结果已保存到 {output_file}")
    elif args.dry_run:
        print("\n仅模拟执行，未保存修改")
    else:
        print("\n未发现需要修改的记录")

if __name__ == "__main__":
    main() 