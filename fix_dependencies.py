import subprocess
import sys
import os

def run_command(command):
    print(f"执行命令: {command}")
    subprocess.run(command, shell=True, check=False)

def main():
    print("开始修复依赖问题...")
    
    # 首先卸载可能有问题的包
    run_command(f"{sys.executable} -m pip uninstall -y attr attrs")
    
    # 然后安装正确的包
    run_command(f"{sys.executable} -m pip install attrs")
    run_command(f"{sys.executable} -m pip install --upgrade aiohttp")
    
    # 安装其他必要的包
    run_command(f"{sys.executable} -m pip install pandas openpyxl requests tqdm")
    
    print("\n依赖修复完成！请尝试重新运行批处理程序:")
    print("python batch_process.py")

if __name__ == "__main__":
    main() 