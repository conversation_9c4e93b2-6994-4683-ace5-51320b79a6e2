import json
import os

original_file_path = '1.json'
final_output_path = '1.json' # The final merged output will be written here

try:
    # Step 1: Read the original 1.json file
    if not os.path.exists(original_file_path):
        print(f"Error: The file {original_file_path} was not found.")
        exit(1)
        
    with open(original_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Step 2: Parse the JSON content (expecting a list of objects)
    try:
        records = json.loads(content)
    except json.JSONDecodeError as e:
        print(f"Error: Failed to decode JSON from {original_file_path}. It might not be valid JSON, or not a simple list. Details: {e}")
        exit(1)

    if not isinstance(records, list):
        print(f"Error: Content of {original_file_path} is not a JSON list as expected.")
        exit(1)

    num_records = len(records)

    if num_records == 0:
        print(f"Info: {original_file_path} contains an empty list. The output file {final_output_path} will also be an empty list.")
        with open(final_output_path, 'w', encoding='utf-8') as f_out:
            json.dump([], f_out, ensure_ascii=False, indent=2)
        print("Process completed for empty list.")
        exit(0)

    # Step 3: Create individual record files
    # Note: The first file created (e.g., "1.json") will overwrite the original_file_path if they have the same name.
    temp_file_names = []
    for i, record in enumerate(records):
        # Files are named 1.json, 2.json, ...
        temp_file_name = f"{i + 1}.json"
        temp_file_names.append(temp_file_name)
        try:
            with open(temp_file_name, 'w', encoding='utf-8') as tf:
                json.dump(record, tf, ensure_ascii=False, indent=2)
            print(f"Record {i+1} (0-indexed {i}) saved to {temp_file_name}")
        except Exception as e:
            print(f"Error writing record {i+1} to {temp_file_name}: {e}")

    # Step 4: Merge individual files back
    merged_records_list = []
    for i in range(num_records):
        # These are the files just created e.g. 1.json, 2.json ...
        temp_file_name_to_read = f"{i + 1}.json" 
        if not os.path.exists(temp_file_name_to_read):
            print(f"Warning: Expected intermediate file {temp_file_name_to_read} not found for merging. Record {i+1} will be skipped.")
            continue
        try:
            with open(temp_file_name_to_read, 'r', encoding='utf-8') as tf:
                record_content = json.load(tf)
            merged_records_list.append(record_content)
        except Exception as e:
            print(f"Error reading or parsing {temp_file_name_to_read} during merge: {e}. Record {i+1} might be corrupt or missing.")
            
    # Step 5: Write the merged list to the final output file
    try:
        with open(final_output_path, 'w', encoding='utf-8') as f_out:
            json.dump(merged_records_list, f_out, ensure_ascii=False, indent=2)
        print(f"All {len(merged_records_list)} records merged and saved to {final_output_path}")
    except Exception as e:
        print(f"Error writing final merged data to {final_output_path}: {e}")
        exit(1)

    # Step 6: Clean up intermediate files
    for temp_file_name_to_delete in temp_file_names:
        if temp_file_name_to_delete != final_output_path:
            try:
                if os.path.exists(temp_file_name_to_delete):
                    os.remove(temp_file_name_to_delete)
                    print(f"Deleted intermediate file: {temp_file_name_to_delete}")
            except OSError as e:
                print(f"Error deleting intermediate file {temp_file_name_to_delete}: {e}")
    
    print("Process completed successfully.")

except Exception as e:
    print(f"An unexpected error occurred: {e}") 