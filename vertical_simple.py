import requests
import json
import uuid
import datetime
import traceback
import os

class VerticalSimpleClient:
    """
    Vertical AI 极简客户端
    仅包含使用已知聊天ID进行对话的功能
    """
    
    def __init__(self, base_url="https://app.verticalstudio.ai"):
        self.base_url = base_url
        self.auth_token = None
        self.session = requests.Session()
        
        # 加载认证令牌
        try:
            if os.path.exists("vertical_auth_token.txt"):
                with open("vertical_auth_token.txt", "r") as f:
                    self.auth_token = f.read().strip()
                if self.auth_token:
                    print("已加载认证令牌")
        except Exception as e:
            print(f"加载认证令牌时出错: {e}")
    
    def set_auth_token(self, token):
        """设置认证令牌"""
        self.auth_token = token
        try:
            with open("vertical_auth_token.txt", "w") as f:
                f.write(token)
            print("已保存认证令牌")
        except Exception as e:
            print(f"保存认证令牌时出错: {e}")
    
    def send_message(self, chat_id, message, model_id="claude-4-sonnet-20250514"):
        """发送消息并获取响应"""
        if not self.auth_token:
            print("错误: 未设置认证令牌")
            return False
            
        if not chat_id:
            print("错误: 未提供聊天ID")
            return False
        
        # 生成消息ID和时间戳
        message_id = str(uuid.uuid4()).replace("-", "")[:16]
        created_at = datetime.datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"
        
        # 构建请求体
        payload = {
            "message": {
                "id": message_id,
                "createdAt": created_at,
                "role": "user",
                "content": message,
                "parts": [
                    {
                        "type": "text",
                        "text": message
                    }
                ]
            },
            "cornerType": "text",
            "chatId": chat_id,
            "settings": {
                "modelId": model_id,
                "reasoning": "on",
                "systemPromptPreset": "default",
                "toneOfVoice": "default"
            }
        }
        
        # 设置请求头
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Origin": self.base_url,
            "Referer": f"{self.base_url}/chat/{chat_id}"
        }
        
        # 设置Cookie
        cookies = {
            "sb-ppdjlmajmpcqpkdmnzfd-auth-token": self.auth_token
        }
        
        url = f"{self.base_url}/api/chat"
        
        print(f"发送消息到: {url}")
        print(f"聊天ID: {chat_id}")
        print(f"模型ID: {model_id}")
        
        try:
            response = self.session.post(
                url, 
                json=payload, 
                headers=headers,
                cookies=cookies, 
                stream=True
            )
            
            # 检查响应状态
            if response.status_code != 200:
                print(f"请求失败，状态码: {response.status_code}")
                print(f"响应内容: {response.text[:500]}")
                return False
            
            print("请求成功，正在接收响应...")
            
            # 解析流式响应
            full_response = ""
            
            for line in response.iter_lines():
                if not line:
                    continue
                
                line_str = line.decode('utf-8')
                
                # 处理主要内容
                if line_str.startswith('0:"') and line_str.endswith('"'):
                    content = self._parse_json_string(line_str[3:-1])
                    full_response += content
                    print(content, end="", flush=True)
            
            print()  # 添加换行
            return True
            
        except Exception as e:
            print(f"发送消息时出错: {e}")
            traceback.print_exc()
            return False
    
    def _parse_json_string(self, content):
        """解析JSON字符串内容"""
        try:
            return json.loads(f'"{content}"')
        except:
            # 回退处理
            temp_content = content.replace('\\\\"', '"')
            temp_content = temp_content.replace('\\n', '\n')
            temp_content = temp_content.replace('\\t', '\t')
            return temp_content

def main():
    """主程序"""
    client = VerticalSimpleClient()
    
    print("Vertical AI 极简客户端")
    print("=" * 30)
    
    # 检查认证令牌
    if not client.auth_token:
        print("未设置认证令牌，请输入:")
        token = input("认证令牌: ")
        if not token:
            print("未提供认证令牌，退出")
            return
        client.set_auth_token(token)
    
    # 获取聊天ID
    chat_id = input("请输入聊天ID: ")
    if not chat_id:
        print("未提供聊天ID，退出")
        return
    
    # 选择模型
    print("可用模型:")
    print("1. claude-4-sonnet-20250514 (默认)")
    print("2. claude-4-opus-20250514")
    model_choice = input("请选择模型 (1/2): ")
    
    model_id = "claude-4-sonnet-20250514"
    if model_choice == "2":
        model_id = "claude-4-opus-20250514"
    
    print(f"\n已选择模型: {model_id}")
    print(f"聊天ID: {chat_id}")
    print("\n输入消息开始聊天，输入 'quit' 退出")
    
    # 聊天循环
    while True:
        message = input("\n> ")
        if message.lower() == "quit":
            break
        
        success = client.send_message(chat_id, message, model_id)
        if not success:
            retry = input("发送失败，是否重试? (y/n): ")
            if retry.lower() != "y":
                break

if __name__ == "__main__":
    main() 