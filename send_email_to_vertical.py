import requests

def send_signup_email(email, password):
    """
    向Vertical AI发送注册邮件
    
    参数:
        email (str): 用户邮箱
        password (str): 用户密码
    
    返回:
        dict: 服务器响应
    """
    url = "https://app.verticalstudio.ai/signup-email.data"
    
    # 准备请求数据
    payload = {
        "email": email,
        "password": password
    }
    
    try:
        # 发送POST请求
        response = requests.post(url, data=payload)
        
        # 检查响应状态
        if response.status_code == 200:
            print(f"邮件发送成功！状态码: {response.status_code}")
            return response.json()  # 如果响应是JSON格式
        else:
            print(f"邮件发送失败。状态码: {response.status_code}")
            return {"error": f"请求失败，状态码: {response.status_code}"}
    
    except Exception as e:
        print(f"发生错误: {str(e)}")
        return {"error": str(e)}


if __name__ == "__main__":
    # 示例使用
    user_email = input("请输入您的邮箱: ")
    user_password = input("请输入您的密码: ")
    
    result = send_signup_email(user_email, user_password)
    print("服务器响应:", result) 