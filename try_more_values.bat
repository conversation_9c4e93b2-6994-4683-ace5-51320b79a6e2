@echo off
echo 将尝试一些特殊的输入源值，每个值之间间隔3秒
echo 请观察显示器，当显示器切换到其他输入源时，记下当前正在尝试的值
echo.
echo 按任意键开始...
pause > nul

echo.
echo 尝试常见的HDMI值...
echo 尝试值 17 (常见HDMI值)...
"%~dp0ControlMyMonitor\ControlMyMonitor.exe" /SetValue Primary 60 17
timeout /t 3 > nul

echo 尝试值 18 (常见HDMI值)...
"%~dp0ControlMyMonitor\ControlMyMonitor.exe" /SetValue Primary 60 18
timeout /t 3 > nul

echo 尝试值 5 (常见HDMI值)...
"%~dp0ControlMyMonitor\ControlMyMonitor.exe" /SetValue Primary 60 5
timeout /t 3 > nul

echo.
echo 尝试常见的DP值...
echo 尝试值 15 (常见DP值)...
"%~dp0ControlMyMonitor\ControlMyMonitor.exe" /SetValue Primary 60 15
timeout /t 3 > nul

echo 尝试值 16 (常见DP值)...
"%~dp0ControlMyMonitor\ControlMyMonitor.exe" /SetValue Primary 60 16
timeout /t 3 > nul

echo 尝试值 10 (常见DP值)...
"%~dp0ControlMyMonitor\ControlMyMonitor.exe" /SetValue Primary 60 10
timeout /t 3 > nul

echo.
echo 尝试常见的DVI值...
echo 尝试值 3 (常见DVI值)...
"%~dp0ControlMyMonitor\ControlMyMonitor.exe" /SetValue Primary 60 3
timeout /t 3 > nul

echo 尝试值 4 (常见DVI值)...
"%~dp0ControlMyMonitor\ControlMyMonitor.exe" /SetValue Primary 60 4
timeout /t 3 > nul

echo.
echo 尝试常见的VGA值...
echo 尝试值 1 (常见VGA值)...
"%~dp0ControlMyMonitor\ControlMyMonitor.exe" /SetValue Primary 60 1
timeout /t 3 > nul

echo 尝试值 2 (常见VGA值)...
"%~dp0ControlMyMonitor\ControlMyMonitor.exe" /SetValue Primary 60 2
timeout /t 3 > nul

echo.
echo 尝试完成
echo 请输入成功切换输入源的值（如果有的话）:
set /p successful_value=

if not "%successful_value%"=="" (
    echo.
    echo 您输入的值是: %successful_value%
    echo 创建快捷切换脚本...

    echo @echo off > switch_to_value_%successful_value%.bat
    echo echo 正在切换到输入源值 %successful_value% ... >> switch_to_value_%successful_value%.bat
    echo "%%~dp0ControlMyMonitor\ControlMyMonitor.exe" /SetValue Primary 60 %successful_value% >> switch_to_value_%successful_value%.bat
    echo echo 正在切换第二个显示器... >> switch_to_value_%successful_value%.bat
    echo "%%~dp0ControlMyMonitor\ControlMyMonitor.exe" /SetValue "\\.\DISPLAY2\Monitor0" 60 %successful_value% >> switch_to_value_%successful_value%.bat
    echo echo 切换完成 >> switch_to_value_%successful_value%.bat
    echo pause >> switch_to_value_%successful_value%.bat

    echo.
    echo 已创建切换脚本: switch_to_value_%successful_value%.bat
    echo 您可以双击该文件来切换到这个输入源
)

pause 