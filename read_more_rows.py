import pandas as pd

# 读取Excel文件
try:
    df = pd.read_excel('处理后文件.xlsx')
    
    # 打印总行数
    print(f"总行数: {len(df)}")
    
    # 随机抽取20行数据查看
    print("\n随机抽取20行数据:")
    sample_df = df.sample(20)
    for idx, row in sample_df.iterrows():
        print("\n行索引:", idx)
        print(f"申訴ID: {row['申訴ID']}")
        print(f"二級分類: {row['二級分類']}")
        print(f"原話: {row['原話']}")
        print(f"建議: {row['建議']}")
        print("-" * 80)
    
except Exception as e:
    print(f"读取文件时出错: {e}") 