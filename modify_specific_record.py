import json
import argparse

def modify_record_category(record_index, new_category):
    print(f"Attempting to modify record at 0-based index: {record_index} to category: '{new_category}'")
    try:
        with open("1.json", "r", encoding="utf-8") as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"Error: 1.json not found.")
        return
    except json.JSONDecodeError:
        print(f"Error: Could not decode JSON from 1.json.")
        return

    if isinstance(data, list) and 0 <= record_index < len(data):
        if isinstance(data[record_index], dict):
            original_category = data[record_index].get("category")
            data[record_index]["category"] = new_category
            try:
                with open("1.json", "w", encoding="utf-8") as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                print(f"Successfully modified category of record {record_index + 1} from '{original_category}' to '{new_category}' in 1.json.")
            except IOError:
                print(f"Error: Could not write the modified data back to 1.json for record {record_index + 1}.")
        else:
            print(f"Error: Record at index {record_index} in the JSON array is not an object.")
            return
    else:
        print(f"Error: JSON data is not a list, is empty, or record index {record_index} is out of bounds.")
        return

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Modify the category of a specific record in a JSON file.")
    parser.add_argument("-r", "--record_index", type=int, required=True, help="The 0-based index of the record to modify.")
    parser.add_argument("-c", "--category", type=str, required=True, help="The new category to set for the record.")
    
    args = parser.parse_args()
    print(f"Arguments received: record_index={args.record_index}, category='{args.category}'")
    
    modify_record_category(args.record_index, args.category) 