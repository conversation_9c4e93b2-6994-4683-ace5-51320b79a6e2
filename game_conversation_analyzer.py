import pandas as pd
import re
import jieba
from collections import Counter

def load_excel_data(file_path):
    """
    加载Excel文件中的游戏列表和对话内容
    """
    try:
        # 读取游戏列表工作表
        games_df = pd.read_excel(file_path, sheet_name=0)
        # 读取对话内容工作表
        conversation_df = pd.read_excel(file_path, sheet_name=1)
        
        return games_df, conversation_df
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return None, None

def extract_game_names(games_df):
    """
    从游戏列表中提取游戏名称
    """
    # 假设游戏名称在"二级分类"列中
    if "二级分类" in games_df.columns:
        game_names = games_df["二级分类"].dropna().tolist()
    else:
        # 尝试获取第二列作为游戏名称
        game_names = games_df.iloc[:, 1].dropna().tolist()
    
    # 过滤空字符串并去除重复
    game_names = [name.strip() for name in game_names if isinstance(name, str) and name.strip()]
    game_names = list(set(game_names))
    
    # 按长度降序排序，确保先匹配较长的游戏名称
    game_names.sort(key=len, reverse=True)
    
    return game_names

def extract_conversations(conversation_df):
    """
    从对话工作表中提取对话内容
    """
    # 尝试找到包含对话内容的列
    conversation_column = None
    
    # 常见的可能包含对话内容的列名
    possible_columns = ["对话", "内容", "对话内容", "conversation", "content", "message"]
    
    for col in possible_columns:
        if col in conversation_df.columns:
            conversation_column = col
            break
    
    # 如果没有找到预定义的列名，使用第一列
    if conversation_column is None:
        conversation_column = conversation_df.columns[0]
    
    # 提取对话内容
    conversations = conversation_df[conversation_column].dropna().astype(str).tolist()
    
    return conversations

def find_games_in_conversations(game_names, conversations):
    """
    在对话内容中查找提到的游戏
    """
    # 将游戏名称添加到jieba分词词典
    for game in game_names:
        jieba.add_word(game)
    
    # 创建游戏名称的正则表达式模式
    # 使用 | 连接所有游戏名称，并对特殊字符进行转义
    game_pattern = '|'.join(re.escape(game) for game in game_names)
    
    # 记录每个游戏在对话中出现的次数
    game_mentions = Counter()
    
    # 记录每个游戏出现在哪些对话中
    game_conversations = {game: [] for game in game_names}
    
    # 分析每个对话
    for idx, conversation in enumerate(conversations):
        # 使用正则表达式查找所有匹配的游戏名称
        matches = re.findall(game_pattern, conversation)
        
        # 更新计数器
        game_mentions.update(matches)
        
        # 记录每个游戏出现的对话
        for game in set(matches):  # 使用set去重
            game_conversations[game].append(idx + 1)  # 对话索引从1开始
    
    return game_mentions, game_conversations

def analyze_excel_file(file_path):
    """
    分析Excel文件，找出对话中提到的游戏
    """
    # 加载Excel数据
    games_df, conversation_df = load_excel_data(file_path)
    
    if games_df is None or conversation_df is None:
        return
    
    # 提取游戏名称
    game_names = extract_game_names(games_df)
    print(f"从Excel中提取到 {len(game_names)} 个游戏名称")
    
    # 提取对话内容
    conversations = extract_conversations(conversation_df)
    print(f"从Excel中提取到 {len(conversations)} 条对话")
    
    # 查找对话中提到的游戏
    game_mentions, game_conversations = find_games_in_conversations(game_names, conversations)
    
    # 输出结果
    if game_mentions:
        print("\n在对话中提到的游戏:")
        for game, count in game_mentions.most_common():
            print(f"- {game}: 出现 {count} 次，在对话 {game_conversations[game]} 中")
        
        # 保存结果到Excel
        result_df = pd.DataFrame({
            "游戏名称": [game for game, _ in game_mentions.most_common()],
            "提及次数": [count for _, count in game_mentions.most_common()],
            "出现在对话": [str(game_conversations[game]) for game, _ in game_mentions.most_common()]
        })
        
        result_file = "游戏提及分析结果.xlsx"
        result_df.to_excel(result_file, index=False)
        print(f"\n分析结果已保存到 {result_file}")
    else:
        print("\n在对话中没有找到任何游戏提及")

if __name__ == "__main__":
    # 请将文件路径替换为您的Excel文件路径
    file_path = input("请输入Excel文件路径: ")
    analyze_excel_file(file_path) 