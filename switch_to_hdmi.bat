@echo on
REM 尝试切换到HDMI口
echo 正在尝试切换到HDMI口...

REM 尝试几个可能的HDMI口值
echo 尝试值17...
"%~dp0ControlMyMonitor\ControlMyMonitor.exe" /SetValue Primary 60 17
timeout /t 2

echo 尝试值11...
"%~dp0ControlMyMonitor\ControlMyMonitor.exe" /SetValue Primary 60 11
timeout /t 2

echo 尝试值4...
"%~dp0ControlMyMonitor\ControlMyMonitor.exe" /SetValue Primary 60 4
timeout /t 2

echo 尝试值2...
"%~dp0ControlMyMonitor\ControlMyMonitor.exe" /SetValue Primary 60 2
timeout /t 2

REM 对第二个显示器也执行同样的操作
echo 正在尝试切换第二个显示器到HDMI口...
echo 尝试值17...
"%~dp0ControlMyMonitor\ControlMyMonitor.exe" /SetValue "\\.\DISPLAY2\Monitor0" 60 17
timeout /t 2

echo 尝试值11...
"%~dp0ControlMyMonitor\ControlMyMonitor.exe" /SetValue "\\.\DISPLAY2\Monitor0" 60 11
timeout /t 2

echo 尝试完成，请检查显示器是否已切换到HDMI口
pause 