import pandas as pd
import re
import os
import argparse
from collections import Counter
from datetime import datetime

# 默认停用词列表，这些词虽然可能出现在游戏名称中，但也是常见词汇，容易导致误匹配
DEFAULT_STOPWORDS = [
    "希望", "龍", "義", "永恆", "點數卡", "點數", "卡", 
    "遊戲", "游戏", "手機", "手机", "手機遊戲", "手机游戏",
    "币", "幣", "金幣", "金币", "元宝", "元寶",
    "账号", "帳號", "账户", "帳戶", "ID", "id",
    "充值", "儲值", "充值卡", "儲值卡",
    "道具", "装备", "裝備", "皮肤", "皮膚", "套装", "套裝",
    "等级", "等級", "升级", "升級", "经验", "經驗",
    "任务", "任務", "关卡", "關卡", "副本",
    "服务器", "伺服器", "区", "區",
    "版本", "更新", "下载", "下載", "安装", "安裝",
    "问题", "問題", "帮助", "幫助", "客服",
    "购买", "購買", "买", "買", "卖", "賣", "交易", "售",
    "退款", "退費", "退钱", "退錢",
    "登录", "登入", "注册", "註冊", "密码", "密碼",
    "官方", "官网", "官網",
    "活动", "活動", "礼包", "禮包", "奖励", "獎勵",
    "时间", "時間", "日期", "期限",
    "系统", "系統", "功能", "设置", "設置",
    "联系", "聯繫", "咨询", "諮詢",
    "回复", "回覆", "回答", "解决", "解決",
    "感谢", "感謝", "谢谢", "謝謝",
    "您好", "你好", "早上好", "中午好", "晚上好",
    "请问", "請問", "麻烦", "麻煩",
    "不好意思", "抱歉", "对不起", "對不起", "不好意思",
    "可以", "不可以", "能", "不能", "行", "不行",
    "是", "否", "对", "對", "错", "錯",
    "好", "坏", "壞", "差", "糟",
    "快", "慢", "多", "少", "大", "小",
    "新", "旧", "舊", "老",
    "开始", "開始", "结束", "結束",
    "我", "你", "他", "她", "它", "我们", "我們", "你们", "你們", "他们", "他們",
    "这", "這", "那", "这个", "這個", "那个", "那個",
    "什么", "什麼", "为什么", "為什麼", "怎么", "怎麼", "如何", "哪里", "哪裡",
    "一", "二", "三", "四", "五", "六", "七", "八", "九", "十",
    "今天", "明天", "昨天", "后天", "前天", "早上", "中午", "下午", "晚上", "夜晚",
    "周一", "周二", "周三", "周四", "周五", "周六", "周日",
    "星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日",
    "月", "年", "日", "天", "小时", "小時", "分钟", "分鐘", "秒",
    "元", "块", "塊", "毛", "角", "分", "美元", "美金", "人民币", "人民幣", "台币", "台幣",
    "个", "個", "件", "张", "張", "本", "册", "冊", "台", "部", "辆", "輛",
    "次", "回", "遍", "趟", "下", "番",
    "很", "非常", "特别", "特別", "十分", "极", "極", "太",
    "因为", "因為", "所以", "但是", "可是", "然而", "不过", "不過", "而且", "并且", "並且",
    "如果", "假如", "假设", "假設", "要是", "除非", "只要", "只有",
    "或者", "或是", "还是", "還是", "不是", "就是", "是否",
    "必须", "必須", "应该", "應該", "可能", "也许", "或许", "或許",
    "已经", "已經", "曾经", "曾經", "正在", "将要", "將要", "会", "會", "能够", "能夠",
    "全部", "所有", "整个", "整個", "每个", "每個", "任何", "某些", "一些", "部分",
    "最", "更", "比较", "比較", "相当", "相當", "稍微", "略微",
    "上", "下", "左", "右", "前", "后", "後", "内", "內", "外", "中", "旁", "边", "邊",
    "从", "從", "到", "往", "向", "朝", "在", "于", "於", "与", "與", "和", "跟", "同", "给", "給",
    "对于", "對於", "关于", "關於", "有关", "有關", "以", "为了", "為了", "由于", "由於"
]

def load_excel_data(file_path):
    """
    加载Excel文件中的游戏列表和对话内容
    """
    try:
        # 读取会员咨询工作表
        conversation_df = pd.read_excel(file_path, sheet_name='會員諮詢')
        # 读取游戏列表工作表
        games_df = pd.read_excel(file_path, sheet_name='遊戲列表')
        
        print(f"成功读取对话表，共 {len(conversation_df)} 行")
        print(f"成功读取游戏列表，共 {len(games_df)} 行")
        
        return games_df, conversation_df
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return None, None

def extract_game_names(games_df, min_length=2, stopwords=None):
    """
    从游戏列表中提取游戏名称
    
    参数:
    - games_df: 游戏列表DataFrame
    - min_length: 游戏名称的最小长度，默认为2
    - stopwords: 停用词列表，这些词不会被视为游戏名称
    """
    if stopwords is None:
        stopwords = []
    
    # 打印列名，以便了解数据结构
    print("游戏列表的列名:", games_df.columns.tolist())
    
    # 打印前几行数据，了解数据格式
    print("\n游戏列表前5行数据:")
    print(games_df.head())
    
    # 假设游戏名称在"二级分类"列中
    if "二级分类" in games_df.columns:
        game_column = "二级分类"
    else:
        # 尝试获取第二列作为游戏名称
        game_column = games_df.columns[1] if len(games_df.columns) > 1 else games_df.columns[0]
    
    print(f"使用列 '{game_column}' 作为游戏名称列")
    
    # 提取游戏名称
    game_names = games_df[game_column].dropna().astype(str).tolist()
    
    # 过滤空字符串、纯数字和去除重复
    game_names = [name.strip() for name in game_names if isinstance(name, str) and name.strip()]
    
    # 排除纯数字的游戏名称，因为这些可能会导致误匹配
    non_numeric_games = [name for name in game_names if not name.isdigit()]
    
    # 过滤掉长度小于最小长度的游戏名称
    filtered_games = [name for name in non_numeric_games if len(name) >= min_length]
    
    # 过滤掉停用词
    if stopwords:
        filtered_games = [name for name in filtered_games if name not in stopwords]
    
    # 去除重复
    filtered_games = list(set(filtered_games))
    
    print(f"过滤前游戏名称数量: {len(game_names)}")
    print(f"过滤后游戏名称数量: {len(filtered_games)}")
    
    # 按长度降序排序，确保先匹配较长的游戏名称
    filtered_games.sort(key=len, reverse=True)
    
    if len(filtered_games) > 0:
        print(f"示例游戏名称: {filtered_games[:5]}")
    
    return filtered_games

def extract_conversations(conversation_df):
    """
    从对话工作表中提取对话内容
    """
    # 打印列名，以便了解数据结构
    print("\n对话表的列名:", conversation_df.columns.tolist())
    
    # 打印前几行数据，了解数据格式
    print("\n对话表前3行数据:")
    print(conversation_df.head(3))
    
    # 尝试找到包含对话内容的列
    conversation_column = None
    
    # 常见的可能包含对话内容的列名
    possible_columns = ["对话", "内容", "對話", "內容", "對話內容", "conversation", "content", "message", "問題", "问题"]
    
    for col in possible_columns:
        if col in conversation_df.columns:
            conversation_column = col
            break
    
    # 如果没有找到预定义的列名，使用第一列
    if conversation_column is None:
        conversation_column = conversation_df.columns[0]
    
    print(f"使用列 '{conversation_column}' 作为对话内容")
    
    # 提取对话内容
    conversations = conversation_df[conversation_column].dropna().astype(str).tolist()
    
    # 如果有ID列，也提取出来
    id_column = None
    for col in conversation_df.columns:
        if "id" in col.lower() or "编号" in col or "編號" in col:
            id_column = col
            break
    
    if id_column:
        print(f"使用列 '{id_column}' 作为对话ID")
        ids = conversation_df[id_column].astype(str).tolist()
    else:
        print("未找到ID列，使用行号作为ID")
        ids = [str(i+1) for i in range(len(conversations))]
    
    return conversations, ids

def find_games_in_conversations(game_names, conversations, ids, case_sensitive=False):
    """
    在对话内容中查找提到的游戏
    
    参数:
    - game_names: 游戏名称列表
    - conversations: 对话内容列表
    - ids: 对话ID列表
    - case_sensitive: 是否区分大小写，默认为False
    """
    if not game_names:
        print("没有有效的游戏名称可供匹配")
        return Counter(), {}, {}
    
    # 记录每个游戏在对话中出现的次数
    game_mentions = Counter()
    
    # 记录每个游戏出现在哪些对话中
    game_conversations = {}
    
    # 记录每个对话中提到的游戏
    conversation_games = {}
    
    # 分析每个对话
    for idx, (conversation, conv_id) in enumerate(zip(conversations, ids)):
        # 为每个对话找到匹配的游戏
        found_games = []
        
        # 如果不区分大小写，将对话内容转换为小写
        if not case_sensitive:
            conversation_lower = conversation.lower()
        
        for game in game_names:
            # 根据是否区分大小写，选择不同的匹配方式
            if case_sensitive:
                # 使用正则表达式查找游戏名称，区分大小写
                matches = re.findall(re.escape(game), conversation)
            else:
                # 不区分大小写的匹配
                # 先将游戏名称转换为小写进行匹配
                game_lower = game.lower()
                matches = re.findall(re.escape(game_lower), conversation_lower)
            
            if matches:
                found_games.append(game)
                # 更新计数器
                game_mentions[game] += len(matches)
                
                # 记录这个游戏出现的对话
                if game not in game_conversations:
                    game_conversations[game] = []
                game_conversations[game].append(conv_id)
        
        # 记录这个对话中提到的所有游戏
        if found_games:
            conversation_games[conv_id] = found_games
    
    return game_mentions, game_conversations, conversation_games

def analyze_excel_file(file_path, min_game_name_length=2, use_stopwords=True, custom_stopwords=None, case_sensitive=False):
    """
    分析Excel文件，找出对话中提到的游戏
    
    参数:
    - file_path: Excel文件路径
    - min_game_name_length: 游戏名称的最小长度，默认为2
    - use_stopwords: 是否使用停用词列表，默认为True
    - custom_stopwords: 自定义停用词列表，如果为None则使用默认停用词
    - case_sensitive: 是否区分大小写，默认为False
    """
    # 加载Excel数据
    games_df, conversation_df = load_excel_data(file_path)
    
    if games_df is None or conversation_df is None:
        return
    
    # 确定停用词列表
    stopwords = None
    if use_stopwords:
        stopwords = DEFAULT_STOPWORDS
        if custom_stopwords:
            stopwords.extend(custom_stopwords)
    
    # 提取游戏名称
    game_names = extract_game_names(games_df, min_length=min_game_name_length, stopwords=stopwords)
    
    # 提取对话内容
    conversations, ids = extract_conversations(conversation_df)
    print(f"\n从Excel中提取到 {len(conversations)} 条对话")
    
    # 查找对话中提到的游戏
    game_mentions, game_conversations, conversation_games = find_games_in_conversations(
        game_names, conversations, ids, case_sensitive=case_sensitive
    )
    
    # 输出结果
    if game_mentions:
        print(f"\n在对话中共提到了 {len(game_mentions)} 个不同的游戏")
        print(f"前10个最常被提及的游戏:")
        for game, count in game_mentions.most_common(10):
            print(f"- {game}: 出现 {count} 次")
        
        # 保存结果到Excel
        # 1. 游戏提及统计
        result_df1 = pd.DataFrame({
            "游戏名称": [game for game, _ in game_mentions.most_common()],
            "提及次数": [count for _, count in game_mentions.most_common()],
            "出现在对话ID": [", ".join(game_conversations[game]) for game, _ in game_mentions.most_common()]
        })
        
        # 2. 对话中提到的游戏
        result_data = []
        for conv_id, games in conversation_games.items():
            result_data.append({
                "对话ID": conv_id,
                "提到的游戏": ", ".join(games),
                "游戏数量": len(games)
            })
        result_df2 = pd.DataFrame(result_data)
        
        # 3. 添加原始对话内容
        conv_dict = {id_val: conv for id_val, conv in zip(ids, conversations)}
        result_df2["对话内容"] = result_df2["对话ID"].map(conv_dict)
        
        # 生成带时间戳的文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        result_file = f"游戏提及分析结果_{timestamp}.xlsx"
        
        # 保存到Excel的不同工作表
        with pd.ExcelWriter(result_file) as writer:
            result_df1.to_excel(writer, sheet_name="游戏提及统计", index=False)
            result_df2.to_excel(writer, sheet_name="对话游戏分析", index=False)
        
        print(f"\n分析结果已保存到 {result_file}")
    else:
        print("\n在对话中没有找到任何游戏提及")

def main():
    """
    主函数，处理命令行参数
    """
    parser = argparse.ArgumentParser(description='分析Excel文件中的游戏提及情况')
    parser.add_argument('--file', '-f', type=str, help='Excel文件路径')
    parser.add_argument('--min-length', '-m', type=int, default=2, help='游戏名称的最小长度，默认为2')
    parser.add_argument('--no-stopwords', action='store_true', help='不使用停用词列表')
    parser.add_argument('--add-stopwords', '-s', type=str, nargs='+', help='添加自定义停用词')
    parser.add_argument('--case-sensitive', '-c', action='store_true', help='区分大小写，默认不区分')
    
    args = parser.parse_args()
    
    # 确定文件路径
    file_path = args.file
    if not file_path:
        file_path = os.path.join("T8", "8591 AI.xlsx")
    
    if not os.path.exists(file_path):
        file_path = input("请输入Excel文件路径: ")
    
    # 分析Excel文件
    analyze_excel_file(
        file_path=file_path,
        min_game_name_length=args.min_length,
        use_stopwords=not args.no_stopwords,
        custom_stopwords=args.add_stopwords,
        case_sensitive=args.case_sensitive
    )

if __name__ == "__main__":
    main() 