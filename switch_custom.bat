@echo off
REM 允许用户输入自定义的值来切换信号源

echo 请输入要切换到的信号源值（例如：15可能是DP，17可能是HDMI）:
set /p input_value=

echo 正在将显示器1切换到信号源值 %input_value% ...
"%~dp0ControlMyMonitor\ControlMyMonitor.exe" /SetValue Primary 60 %input_value%

echo 是否同时切换显示器2？(Y/N)
set /p switch_second=
if /i "%switch_second%"=="Y" (
    echo 正在将显示器2切换到信号源值 %input_value% ...
    "%~dp0ControlMyMonitor\ControlMyMonitor.exe" /SetValue "\\.\DISPLAY2\Monitor0" 60 %input_value%
)

echo 切换操作完成
pause 