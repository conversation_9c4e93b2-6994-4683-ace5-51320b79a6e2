import requests
import json
import time

# API配置
base_url = "https://v1.voct.top/v1/chat/completions"
api_key = "sk-cTifeFuCoaFG3PSRpy684RS8NkpP5NNIltLkyajjG2Q"

# 请求头
headers = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {api_key}"
}

def test_model(model_id, test_message="你好，请简单自我介绍一下"):
    """测试模型可用性"""
    url = f"{base_url}/chat/completions"
    
    payload = {
        "model": model_id,
        "messages": [{"role": "user", "content": test_message}],
        "temperature": 0.7,
        "max_tokens": 200  # 限制回复长度，加快测试速度
    }
    
    print(f"\n正在测试模型: {model_id}")
    print("-" * 60)
    
    try:
        start_time = time.time()
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"状态码: {response.status_code}")
        print(f"响应时间: {response_time:.2f}秒")
        
        if response.status_code == 200:
            result = response.json()
            
            if 'choices' in result and len(result['choices']) > 0:
                message = result['choices'][0].get('message', {})
                content = message.get('content', '')
                
                # 截取部分回复内容展示
                preview = content[:100] + "..." if len(content) > 100 else content
                print(f"回复预览: {preview}")
                
                # 显示使用情况
                if 'usage' in result:
                    usage = result['usage']
                    print(f"Token使用: 提示={usage.get('prompt_tokens', 'N/A')}, " 
                          f"完成={usage.get('completion_tokens', 'N/A')}, "
                          f"总计={usage.get('total_tokens', 'N/A')}")
                
                print("测试结果: ✅ 可用")
                return True, "可用"
            else:
                print(f"未找到回复内容: {result}")
                print("测试结果: ❌ 异常响应格式")
                return False, "异常响应格式"
        else:
            error_msg = response.text
            print(f"请求失败: {error_msg}")
            print("测试结果: ❌ 请求失败")
            return False, f"请求失败 (状态码: {response.status_code})"
    except requests.exceptions.Timeout:
        print("请求超时")
        print("测试结果: ❌ 请求超时")
        return False, "请求超时"
    except Exception as e:
        print(f"发生错误: {str(e)}")
        print("测试结果: ❌ 发生错误")
        return False, str(e)

if __name__ == "__main__":
    # 要测试的模型列表
    models_to_test = [
        "gemini-2.5-flash-preview-04-17"
    ]
    
    # 测试问题
    test_question = "请用一句话介绍你自己，并说明你的主要优势是什么"
    
    # 存储测试结果
    results = {}
    
    print("开始测试模型可用性...\n")
    
    for model in models_to_test:
        success, message = test_model(model, test_question)
        results[model] = {"success": success, "message": message}
        time.sleep(1)  # 添加短暂延迟，避免API限流
    
    # 输出汇总结果
    print("\n\n测试结果汇总:")
    print("=" * 60)
    print(f"{'模型ID':<30} | {'状态':<10} | {'详情'}")
    print("-" * 60)
    
    for model, result in results.items():
        status = "✅ 可用" if result["success"] else "❌ 不可用"
        print(f"{model:<30} | {status:<10} | {result['message']}") 