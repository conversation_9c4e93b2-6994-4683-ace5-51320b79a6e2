import json
import re
from collections import Counter, defaultdict
import matplotlib.pyplot as plt
from datetime import datetime
import pandas as pd
import os

def load_json_data(filepath):
    """加载JSON文件"""
    try:
        with open(filepath, "r", encoding="utf-8") as f:
            data = json.load(f)
        return data
    except FileNotFoundError:
        print(f"错误：找不到文件 {filepath}")
        return None
    except json.JSONDecodeError:
        print(f"错误：无法解析JSON文件 {filepath}")
        return None

def extract_conversation_info(record):
    """从记录中提取对话信息"""
    content = record.get('content', '')
    category = record.get('category', '未分类')
    
    # 提取日期
    date_match = re.search(r'(\d{4}-\d{2}-\d{2})', content)
    date = date_match.group(1) if date_match else "未知日期"
    
    # 提取客服编号
    cs_id_match = re.search(r'客服 (\d+) 號 (\w+)', content)
    cs_id = cs_id_match.group(1) if cs_id_match else "未知"
    cs_name = cs_id_match.group(2) if cs_id_match else "未知"
    
    # 检查是否包含系统自动回复
    has_system_reply = "**系統回復**" in content
    
    return {
        'date': date,
        'category': category,
        'cs_id': cs_id,
        'cs_name': cs_name,
        'has_system_reply': has_system_reply,
        'content_length': len(content),
        'content_sample': content[:100] + '...' if len(content) > 100 else content
    }

def analyze_categories(records):
    """分析类别分布"""
    categories = [record.get('category', '未分类') for record in records]
    category_counter = Counter(categories)
    
    return dict(category_counter.most_common())

def analyze_cs_performance(records):
    """分析客服处理情况"""
    cs_stats = defaultdict(lambda: {'count': 0, 'categories': Counter()})
    
    for record in records:
        info = extract_conversation_info(record)
        if info['cs_id'] != "未知":
            cs_id = f"{info['cs_id']}({info['cs_name']})"
            cs_stats[cs_id]['count'] += 1
            cs_stats[cs_id]['categories'][info['category']] += 1
    
    return cs_stats

def analyze_time_distribution(records):
    """分析时间分布"""
    dates = []
    for record in records:
        info = extract_conversation_info(record)
        if info['date'] != "未知日期":
            dates.append(info['date'])
    
    date_counter = Counter(dates)
    return dict(sorted(date_counter.items()))

def export_to_excel(data, filename="客服记录分析.xlsx"):
    """导出数据到Excel"""
    # 创建Excel写入器
    with pd.ExcelWriter(filename) as writer:
        # 类别分布
        if 'category_stats' in data:
            df_categories = pd.DataFrame(list(data['category_stats'].items()), 
                                        columns=['类别', '数量'])
            df_categories['百分比'] = df_categories['数量'] / df_categories['数量'].sum() * 100
            df_categories.sort_values('数量', ascending=False, inplace=True)
            df_categories.to_excel(writer, sheet_name='类别分布', index=False)
        
        # 时间分布
        if 'time_stats' in data:
            df_time = pd.DataFrame(list(data['time_stats'].items()),
                                columns=['日期', '数量'])
            df_time.sort_values('日期', inplace=True)
            df_time.to_excel(writer, sheet_name='时间分布', index=False)
        
        # 客服统计
        if 'cs_stats' in data:
            cs_data = []
            for cs_id, stats in data['cs_stats'].items():
                top_categories = dict(stats['categories'].most_common(3))
                cs_data.append({
                    '客服ID': cs_id,
                    '处理数量': stats['count'],
                    '主要处理类别1': list(top_categories.keys())[0] if top_categories else '无',
                    '类别1数量': list(top_categories.values())[0] if top_categories else 0,
                    '主要处理类别2': list(top_categories.keys())[1] if len(top_categories) > 1 else '无',
                    '类别2数量': list(top_categories.values())[1] if len(top_categories) > 1 else 0,
                    '主要处理类别3': list(top_categories.keys())[2] if len(top_categories) > 2 else '无',
                    '类别3数量': list(top_categories.values())[2] if len(top_categories) > 2 else 0
                })
            df_cs = pd.DataFrame(cs_data)
            df_cs.sort_values('处理数量', ascending=False, inplace=True)
            df_cs.to_excel(writer, sheet_name='客服统计', index=False)
        
        # 详细记录
        if 'record_details' in data:
            df_details = pd.DataFrame(data['record_details'])
            df_details.to_excel(writer, sheet_name='详细记录', index=False)
    
    print(f"分析结果已导出至 {filename}")

def plot_category_distribution(category_stats, save_path="类别分布.png"):
    """绘制类别分布图"""
    plt.figure(figsize=(12, 6))
    categories = list(category_stats.keys())
    counts = list(category_stats.values())
    
    # 根据数量排序
    sorted_indices = sorted(range(len(counts)), key=lambda i: counts[i], reverse=True)
    categories = [categories[i] for i in sorted_indices]
    counts = [counts[i] for i in sorted_indices]
    
    plt.bar(categories, counts)
    plt.xticks(rotation=45, ha='right')
    plt.title('客服记录类别分布')
    plt.tight_layout()
    plt.savefig(save_path)
    print(f"类别分布图已保存至 {save_path}")

def plot_time_distribution(time_stats, save_path="时间分布.png"):
    """绘制时间分布图"""
    plt.figure(figsize=(12, 6))
    dates = list(time_stats.keys())
    counts = list(time_stats.values())
    
    plt.plot(dates, counts, marker='o')
    plt.xticks(rotation=45, ha='right')
    plt.title('客服记录时间分布')
    plt.tight_layout()
    plt.savefig(save_path)
    print(f"时间分布图已保存至 {save_path}")

def generate_report(analysis_data):
    """生成分析报告"""
    report = []
    
    report.append("# 客服聊天记录分析报告")
    report.append(f"## 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("\n## 数据概览")
    report.append(f"- 总记录数: {analysis_data['total_records']}")
    report.append(f"- 类别数量: {len(analysis_data['category_stats'])}")
    report.append(f"- 客服人员数量: {len(analysis_data['cs_stats'])}")
    report.append(f"- 日期范围: {min(analysis_data['time_stats'].keys(), default='无')} 至 {max(analysis_data['time_stats'].keys(), default='无')}")
    
    report.append("\n## 类别分布")
    report.append("| 类别 | 数量 | 百分比 |")
    report.append("| --- | --- | --- |")
    total = sum(analysis_data['category_stats'].values())
    for category, count in sorted(analysis_data['category_stats'].items(), key=lambda x: x[1], reverse=True):
        percentage = count / total * 100
        report.append(f"| {category} | {count} | {percentage:.2f}% |")
    
    report.append("\n## 客服处理情况")
    report.append("| 客服ID | 处理数量 | 主要处理类别 |")
    report.append("| --- | --- | --- |")
    for cs_id, stats in sorted(analysis_data['cs_stats'].items(), key=lambda x: x[1]['count'], reverse=True)[:10]:
        top_categories = stats['categories'].most_common(1)
        main_category = f"{top_categories[0][0]}({top_categories[0][1]}次)" if top_categories else "无"
        report.append(f"| {cs_id} | {stats['count']} | {main_category} |")
    
    report.append("\n## 问题类型分析")
    category_insights = {
        "售后(纠纷争议)": "主要涉及退款、商品不符和账号被找回等问题",
        "註冊登入": "包括无法登录、密码问题和账号被锁定等",
        "交易安全": "涉及账号被盗、诈骗和可疑操作等安全问题",
        "儲值點數": "关于充值、点数购买和储值失败等问题",
        "發佈商品": "商品上架、违规和删除商品等问题",
        "交易環境": "平台规则、交易秩序和不正当竞争等问题"
    }
    
    for category, insight in category_insights.items():
        if category in analysis_data['category_stats']:
            count = analysis_data['category_stats'][category]
            percentage = count / total * 100
            report.append(f"### {category} ({count}次, {percentage:.2f}%)")
            report.append(f"{insight}")
    
    report.append("\n## 总结与建议")
    report.append("1. 平台应加强对售后纠纷的处理流程，尤其是退款和商品不符问题")
    report.append("2. 改进账号安全措施，减少账号被盗和找回问题")
    report.append("3. 优化注册登录流程，降低用户在这方面遇到的问题")
    report.append("4. 加强客服培训，提高解决问题的效率和质量")
    
    return "\n".join(report)

def main():
    # 加载数据
    data = load_json_data("1.json")
    if not data:
        return
    
    print(f"加载了 {len(data)} 条客服记录")
    
    # 分析数据
    category_stats = analyze_categories(data)
    cs_stats = analyze_cs_performance(data)
    time_stats = analyze_time_distribution(data)
    
    # 提取详细记录信息
    record_details = []
    for record in data:
        record_details.append(extract_conversation_info(record))
    
    # 整合分析数据
    analysis_data = {
        'total_records': len(data),
        'category_stats': category_stats,
        'cs_stats': cs_stats,
        'time_stats': time_stats,
        'record_details': record_details
    }
    
    # 导出数据
    export_to_excel(analysis_data)
    
    # 绘制图表
    try:
        plot_category_distribution(category_stats)
        plot_time_distribution(time_stats)
    except Exception as e:
        print(f"绘制图表时出错: {e}")
    
    # 生成报告
    report = generate_report(analysis_data)
    with open("客服记录分析报告.md", "w", encoding="utf-8") as f:
        f.write(report)
    print("分析报告已生成: 客服记录分析报告.md")

if __name__ == "__main__":
    main() 