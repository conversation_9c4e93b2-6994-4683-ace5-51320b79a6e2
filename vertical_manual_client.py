import requests
import json
import uuid
import time
import re
import os
import random
from typing import Dict, Any, Optional, List, Union, Generator
import datetime
import traceback

class VerticalClient:
    """
    Vertical AI 简化客户端
    仅包含使用已知聊天ID进行对话的功能
    """
    
    def __init__(self, base_url="https://app.verticalstudio.ai"):
        """
        初始化客户端
        
        参数:
            base_url: API基础URL
        """
        self.base_url = base_url
        self.auth_token = None
        self.session = requests.Session()
        self.headers = {
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        }
        # 加载已保存的认证令牌（如果存在）
        self._load_auth_token()
    
    def _load_auth_token(self):
        """从文件加载认证令牌"""
        try:
            if os.path.exists("vertical_auth_token.txt"):
                with open("vertical_auth_token.txt", "r") as f:
                    self.auth_token = f.read().strip()
                if self.auth_token:
                    print("已加载认证令牌")
        except Exception as e:
            print(f"加载认证令牌时出错: {e}")
    
    def _save_auth_token(self):
        """保存认证令牌到文件"""
        try:
            with open("vertical_auth_token.txt", "w") as f:
                f.write(self.auth_token)
            print("已保存认证令牌")
        except Exception as e:
            print(f"保存认证令牌时出错: {e}")
    
    def get_models(self) -> Dict[str, Any]:
        """
        获取可用模型列表
        
        返回:
            模型列表
        """
        try:
            # 这里我们直接返回已知的模型列表
            # 实际上应该从服务器获取，但根据您提供的信息，这些是已知的模型
            models = {
                "models": [
                    {
                        "modelId": "claude-4-opus-20250514"
                    },
                    {
                        "modelId": "claude-4-sonnet-20250514"
                    }
                ]
            }
            return models
        except Exception as e:
            print(f"获取模型列表时出错: {e}")
            return {"error": str(e)}
    
    def set_auth_token(self, token: str):
        """
        手动设置认证令牌
        
        参数:
            token: 认证令牌
        """
        self.auth_token = token
        self._save_auth_token()
        print("已设置认证令牌")
    
    def send_message_stream(self, chat_id: str, message: str, model_id: str) -> Generator[Dict[str, Any], None, None]:
        """
        发送消息并获取流式响应
        
        参数:
            chat_id: 聊天ID
            message: 消息内容
            model_id: 模型ID
            
        返回:
            流式响应生成器
        """
        if not self.auth_token:
            yield {"error": "未设置认证令牌"}
            return
        
        # 使用正确的API端点
        url = f"{self.base_url}/api/chat"
        
        try:
            # 生成消息ID和时间戳
            message_id = str(uuid.uuid4()).replace("-", "")[:16]
            # 使用更简单的方式生成ISO格式时间戳
            created_at = datetime.datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"
            
            print(f"消息ID: {message_id}")
            print(f"创建时间: {created_at}")
            
            # 构建请求体
            payload = {
                "message": {
                    "id": message_id,
                    "createdAt": created_at,
                    "role": "user",
                    "content": message,
                    "parts": [
                        {
                            "type": "text",
                            "text": message
                        }
                    ]
                },
                "cornerType": "text",
                "chatId": chat_id,
                "settings": {
                    "modelId": model_id,
                    "reasoning": "on",
                    "systemPromptPreset": "default",
                    "toneOfVoice": "default"
                }
            }
            
            # 设置请求头
            headers = {
                "Content-Type": "application/json",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Origin": self.base_url,
                "Referer": f"{self.base_url}/chat/{chat_id}"
            }
            
            # 设置Cookie
            cookies = {
                "sb-ppdjlmajmpcqpkdmnzfd-auth-token": self.auth_token
            }
            
            print(f"发送消息到: {url}")
            print(f"聊天ID: {chat_id}")
            print(f"模型ID: {model_id}")
            print(f"请求头: {headers}")
            print(f"请求体: {json.dumps(payload, ensure_ascii=False)[:200]}...")
            
            response = self.session.post(
                url, 
                json=payload, 
                headers=headers,
                cookies=cookies, 
                stream=True
            )
            
            # 检查响应状态
            if response.status_code != 200:
                print(f"请求失败，状态码: {response.status_code}")
                print(f"响应头: {dict(response.headers)}")
                print(f"响应内容: {response.text[:500]}")
                yield {"error": f"请求失败，状态码: {response.status_code}"}
                return
            
            print(f"请求成功，状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            
            # 解析流式响应
            has_content = False
            line_count = 0
            
            for line in response.iter_lines():
                line_count += 1
                if not line:
                    continue
                
                try:
                    line_str = line.decode('utf-8')
                    print(f"接收到响应行 {line_count}: {line_str[:100]}...")  # 只打印前100个字符
                    
                    # 处理主要内容
                    if line_str.startswith('0:"') and line_str.endswith('"'):
                        content = self._parse_json_string(line_str[3:-1])
                        has_content = True
                        yield {"type": "content", "content": content}
                    
                    # 处理思考过程
                    elif line_str.startswith('g:"') and line_str.endswith('"'):
                        thinking = self._parse_json_string(line_str[3:-1])
                        yield {"type": "thinking", "content": thinking}
                    
                    # 处理完成信号
                    elif line_str.startswith('d:'):
                        try:
                            event_data = json.loads(line_str[2:])
                            if event_data.get("type") == "done" or event_data.get("type") == "DONE":
                                yield {"type": "done"}
                        except:
                            pass
                    
                    # 处理错误信息
                    elif line_str.startswith('error:'):
                        error_msg = line_str[6:]
                        yield {"error": f"服务器错误: {error_msg}"}
                    
                    # 处理其他未知格式
                    elif not has_content and not line_str.startswith('g:"'):
                        # 尝试从其他格式中提取内容
                        try:
                            if line_str.startswith('{') and line_str.endswith('}'):
                                # 可能是JSON格式
                                json_data = json.loads(line_str)
                                if "content" in json_data:
                                    has_content = True
                                    yield {"type": "content", "content": json_data["content"]}
                            else:
                                # 如果无法解析，将原始行作为内容返回
                                if len(line_str) > 10:  # 避免返回太短的无意义内容
                                    has_content = True
                                    yield {"type": "content", "content": line_str}
                        except Exception as e:
                            print(f"解析响应行时出错: {e}")
                
                except Exception as e:
                    print(f"处理响应行时出错: {e}")
                    yield {"error": f"处理响应时出错: {str(e)}"}
            
            print(f"总共接收到 {line_count} 行响应")
            
            # 如果没有任何内容，返回一个错误
            if not has_content:
                yield {"error": "未收到任何内容"}
                        
        except Exception as e:
            import traceback
            print(f"发送消息时出错: {e}")
            print(f"错误详情: {traceback.format_exc()}")
            yield {"error": str(e)}
    
    def _parse_json_string(self, content: str) -> str:
        """解析JSON字符串内容"""
        try:
            return json.loads(f'"{content}"')
        except json.JSONDecodeError:
            # 回退处理
            temp_content = content.replace('\\\\"', '"')
            temp_content = temp_content.replace('\\n', '\n')
            temp_content = temp_content.replace('\\t', '\t')
            return temp_content
        except Exception as e:
            print(f"解析JSON字符串时出错: {e}")
            return content

# 交互式客户端
def interactive_client():
    """交互式客户端"""
    client = VerticalClient()
    
    print("欢迎使用 Vertical AI 简化客户端")
    print("=" * 50)
    
    # 检查认证令牌
    if not client.auth_token:
        print("您尚未设置认证令牌，请选择以下操作：")
        print("1. 手动设置认证令牌")
        print("2. 使用示例令牌（仅用于测试）")
        choice = input("请选择 (1/2): ")
        
        if choice == "1":
            token = input("请输入认证令牌: ")
            client.set_auth_token(token)
        
        elif choice == "2":
            # 使用示例令牌
            example_token = "base64-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"
            print(f"使用示例令牌: {example_token[:20]}...")
            client.set_auth_token(example_token)
        
        else:
            print("无效的选择")
            return
    
    # 主聊天循环
    print("\n现在您可以开始聊天了")
    print("可用命令：")
    print("/models - 显示可用模型")
    print("/switch <model_id> - 切换模型")
    print("/chat_id <id> - 设置聊天ID")
    print("/debug - 切换调试模式")
    print("/quit - 退出")
    
    current_model = "claude-4-sonnet-20250514"
    current_chat_id = None
    debug_mode = False
    
    # 提示用户设置聊天ID
    print("\n请先设置聊天ID，使用命令 /chat_id <id>")
    
    while True:
        print(f"\n当前模型: {current_model}")
        if current_chat_id:
            print(f"当前聊天ID: {current_chat_id}")
        else:
            print("未设置聊天ID")
            
        user_input = input("\n> ")
        
        if user_input.lower() == "/quit":
            print("再见！")
            break
            
        elif user_input.lower() == "/models":
            models = client.get_models()
            print("\n可用模型:")
            for model in models.get("models", []):
                print(f"- {model.get('modelId')}")
            continue
            
        elif user_input.lower().startswith("/switch "):
            new_model = user_input[8:].strip()
            models = client.get_models()
            valid_model = False
            
            for model in models.get("models", []):
                if model.get("modelId") == new_model:
                    valid_model = True
                    break
            
            if valid_model:
                current_model = new_model
                print(f"已切换到模型: {current_model}")
            else:
                print(f"无效的模型: {new_model}")
            continue
            
        elif user_input.lower().startswith("/chat_id "):
            current_chat_id = user_input[9:].strip()
            print(f"已设置聊天ID: {current_chat_id}")
            continue
            
        elif user_input.lower() == "/debug":
            debug_mode = not debug_mode
            print(f"调试模式: {'开启' if debug_mode else '关闭'}")
            continue
        
        # 发送消息
        try:
            # 如果没有聊天ID，提示用户设置
            if not current_chat_id:
                print("请先设置聊天ID，使用命令 /chat_id <id>")
                continue
            
            # 发送消息并获取流式响应
            print("\n[模型思考中...]")
            
            has_response = False
            has_error = False
            
            for chunk in client.send_message_stream(current_chat_id, user_input, current_model):
                if chunk.get("type") == "content":
                    has_response = True
                    print(chunk.get("content"), end="", flush=True)
                elif chunk.get("type") == "thinking":
                    # 在调试模式下打印思考过程
                    if debug_mode:
                        print(f"\n[思考] {chunk.get('content')}")
                elif chunk.get("error"):
                    has_error = True
                    print(f"\n错误: {chunk.get('error')}")
                    break
            
            print()  # 添加换行
            
        except Exception as e:
            print(f"错误: {e}")
            print(traceback.format_exc())

if __name__ == "__main__":
    interactive_client() 