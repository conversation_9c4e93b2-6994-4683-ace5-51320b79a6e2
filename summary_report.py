import pandas as pd
import numpy as np
from tabulate import tabulate

# 读取原始文件和最终修正后的文件
try:
    df_original = pd.read_excel('处理后文件.xlsx')
    df_final = pd.read_excel('处理后文件_最终版.xlsx')
    
    print("=== 修正总结报告 ===")
    print(f"原始文件行数: {len(df_original)}, 最终修正后文件行数: {len(df_final)}")
    
    # 1. 分类修正统计
    category_changes = []
    for idx, row in df_final.iterrows():
        if df_original.loc[idx, '二級分類'] != row['二級分類']:
            category_changes.append({
                '行索引': idx,
                '申訴ID': row['申訴ID'],
                '原分类': df_original.loc[idx, '二級分類'],
                '修正后分类': row['二級分類']
            })
    
    print(f"\n1. 分类修正数量: {len(category_changes)}")
    if category_changes:
        print("分类修正详情:")
        print(tabulate(category_changes, headers="keys", tablefmt="grid"))
    
    # 2. 原话修正统计
    text_changes = 0
    text_filled = 0
    for idx, row in df_final.iterrows():
        if pd.isna(df_original.loc[idx, '原話']) and not pd.isna(row['原話']):
            text_filled += 1
        elif not pd.isna(df_original.loc[idx, '原話']) and not pd.isna(row['原話']) and df_original.loc[idx, '原話'] != row['原話']:
            text_changes += 1
    
    print(f"\n2. 原话修正统计:")
    print(f"   - 填充空原话: {text_filled}行")
    print(f"   - 修正已有原话: {text_changes}行")
    print(f"   - 总计修正原话: {text_filled + text_changes}行")
    
    # 3. 建议修正统计
    suggestion_filled = 0
    for idx, row in df_final.iterrows():
        if pd.isna(df_original.loc[idx, '建議']) and not pd.isna(row['建議']):
            suggestion_filled += 1
    
    print(f"\n3. 建议修正统计:")
    print(f"   - 填充空建议: {suggestion_filled}行")
    
    # 4. 分类统计对比
    print("\n4. 分类统计对比:")
    original_categories = df_original['二級分類'].value_counts()
    final_categories = df_final['二級分類'].value_counts()
    
    categories = sorted(list(set(list(original_categories.index) + list(final_categories.index))))
    category_comparison = []
    
    for category in categories:
        original_count = original_categories.get(category, 0)
        final_count = final_categories.get(category, 0)
        diff = final_count - original_count
        category_comparison.append({
            '分类': category,
            '原始数量': original_count,
            '修正后数量': final_count,
            '变化': diff
        })
    
    print(tabulate(category_comparison, headers="keys", tablefmt="grid"))
    
    # 5. 数据完整性检查
    original_empty_text = df_original['原話'].isna().sum()
    original_empty_suggestions = df_original['建議'].isna().sum()
    
    final_empty_text = df_final['原話'].isna().sum()
    final_empty_suggestions = df_final['建議'].isna().sum()
    
    print("\n5. 数据完整性检查:")
    print(f"   - 原始文件空原话: {original_empty_text}行 -> 修正后空原话: {final_empty_text}行")
    print(f"   - 原始文件空建议: {original_empty_suggestions}行 -> 修正后空建议: {final_empty_suggestions}行")
    
    # 6. 总结
    total_modified_rows = 0
    for idx in range(len(df_original)):
        if (df_original.loc[idx, '原話'] != df_final.loc[idx, '原話'] and not (pd.isna(df_original.loc[idx, '原話']) and pd.isna(df_final.loc[idx, '原話']))) or \
           (df_original.loc[idx, '建議'] != df_final.loc[idx, '建議'] and not (pd.isna(df_original.loc[idx, '建議']) and pd.isna(df_final.loc[idx, '建議']))) or \
           (df_original.loc[idx, '二級分類'] != df_final.loc[idx, '二級分類']):
            total_modified_rows += 1
    
    print(f"\n6. 总结:")
    print(f"   - 总共修改了{total_modified_rows}行数据，占总数据的{total_modified_rows/len(df_original)*100:.2f}%")
    print(f"   - 数据完整性: 从{(len(df_original) - original_empty_text - original_empty_suggestions)/(len(df_original)*2)*100:.2f}%提升到{(len(df_final) - final_empty_text - final_empty_suggestions)/(len(df_final)*2)*100:.2f}%")
    
except Exception as e:
    print(f"生成报告时出错: {e}")
    
    # 如果没有tabulate库，使用简单的表格格式
    try:
        if 'tabulate' in str(e):
            print("\n尝试使用简单表格格式重新生成报告...")
            # 重新生成分类修正详情
            if category_changes:
                print("\n分类修正详情:")
                for change in category_changes:
                    print(f"行索引: {change['行索引']}, 申訴ID: {change['申訴ID']}, 原分类: {change['原分类']} -> 修正后分类: {change['修正后分类']}")
            
            # 重新生成分类统计对比
            print("\n分类统计对比:")
            for category in categories:
                original_count = original_categories.get(category, 0)
                final_count = final_categories.get(category, 0)
                diff = final_count - original_count
                print(f"分类: {category}, 原始数量: {original_count}, 修正后数量: {final_count}, 变化: {diff}")
    except:
        pass 