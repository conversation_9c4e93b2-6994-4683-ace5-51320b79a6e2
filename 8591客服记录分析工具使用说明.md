# 8591客服记录分析工具使用说明

本工具集用于分析和处理8591平台的客服聊天记录，提供了分类、统计、修改和报告生成等功能。

## 工具列表

1. `analyze_customer_service.py` - 分析客服聊天记录并生成报告
2. `batch_modify_categories.py` - 批量修改记录类别
3. `modify_specific_record.py` - 修改特定记录的类别
4. `categorize_json.py` - 自动分类所有记录

## 安装依赖

在使用这些工具前，请确保已安装所有依赖项：

```bash
pip install pandas matplotlib
```

## 使用方法

### 1. 分析客服聊天记录 (analyze_customer_service.py)

这个工具会分析1.json文件中的客服聊天记录，并生成Excel报表和图形化分析结果：

```bash
python analyze_customer_service.py
```

输出：
- `客服记录分析.xlsx` - 包含类别分布、时间分布、客服统计等数据的Excel文件
- `类别分布.png` - 类别分布柱状图
- `时间分布.png` - 时间分布折线图
- `客服记录分析报告.md` - 分析报告的Markdown文件

### 2. 批量修改记录类别 (batch_modify_categories.py)

根据关键词或正则表达式批量修改记录的类别：

```bash
# 使用关键词修改类别
python batch_modify_categories.py --keywords "退款" "取消" --category "售后(纠纷争议)" --statistics

# 使用正则表达式修改类别
python batch_modify_categories.py --regex "人臉識別|健保卡|支付密碼" --category "身份/安全驗證" --statistics

# 仅模拟执行，不实际修改文件
python batch_modify_categories.py --keywords "注册" "登录" --category "註冊登入" --dry-run

# 将结果保存到新文件
python batch_modify_categories.py --keywords "充值" "储值" --category "儲值點數" --output modified_records.json
```

参数说明：
- `--input`, `-i`: 输入JSON文件路径（默认为"1.json"）
- `--output`, `-o`: 输出JSON文件路径（默认覆盖输入文件）
- `--keywords`, `-k`: 用于匹配的关键词列表
- `--regex`, `-r`: 用于匹配的正则表达式
- `--category`, `-c`: 要设置的新类别（必填）
- `--ignore-case`, `-ic`: 忽略大小写
- `--statistics`, `-s`: 显示修改前后的类别统计
- `--dry-run`, `-d`: 仅模拟执行，不实际修改文件

### 3. 修改特定记录的类别 (modify_specific_record.py)

修改指定索引的记录的类别：

```bash
# 修改索引为5的记录的类别
python modify_specific_record.py -r 5 -c "交易安全"
```

参数说明：
- `-r`, `--record_index`: 要修改的记录索引（0-based）
- `-c`, `--category`: 新的类别名称

### 4. 自动分类所有记录 (categorize_json.py)

根据预设规则自动分类所有记录：

```bash
python categorize_json.py
```

## 示例工作流程

1. 首先使用 `categorize_json.py` 对所有记录进行自动分类
2. 使用 `analyze_customer_service.py` 生成初步分析报告
3. 查看报告，发现一些分类不准确的情况
4. 使用 `batch_modify_categories.py` 批量修正特定类型的记录
5. 对个别特殊记录使用 `modify_specific_record.py` 进行修正
6. 再次运行 `analyze_customer_service.py` 生成最终分析报告

## 常见问题

1. **为什么图表无法生成？**
   - 请确保已安装matplotlib库，且有正确的中文字体支持

2. **如何处理中文乱码问题？**
   - 所有脚本均使用UTF-8编码打开和保存文件，确保您的JSON文件也是UTF-8编码

3. **如何获取更准确的分类结果？**
   - 可以先使用自动分类，然后根据分析结果，调整关键词和分类规则
   - 在`categorize_json.py`中修改关键词列表可以提高自动分类的准确性

## 开发者文档

如果您需要扩展这些工具，可以参考以下信息：

1. **JSON数据结构**：
   每条记录包含`content`和`category`两个字段

2. **分类体系**：
   目前使用的分类包括"售后(纠纷争议)"、"註冊登入"、"交易安全"等多个类别

3. **添加新功能**：
   如需添加新功能，建议创建新的脚本文件，并重用已有的加载和保存函数 