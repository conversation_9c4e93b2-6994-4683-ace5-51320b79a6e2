import requests
import json  # 用于美观地打印JSON响应
import requests
import json
import pandas as pd
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import time # 用于可选的简单延时或日志
# --- 配置你的API参数 ---
# 替换为你的 Dify API Key
# 你可以在 Dify 控制台的 "Account" -> "API Keys" 中找到
DIFY_API_KEY = 'dataset-OFijVX4LxPAn3lsgfVriAdls'
dataset_id="180929c1-6511-4951-a538-e499aa33ebe4"
document_id="51365ea2-a27e-4f40-bb03-8bf84632e9bd"
URL = f'https://api.dify.ai/v1/datasets/{dataset_id}/documents/{document_id}/segments'

# 完整的 API URL，包含具体的 dataset_id 和 document_id
BASE_URL = f'https://api.dify.ai/v1/datasets/{dataset_id}/documents/{document_id}/segments'

# --- 构建请求头 ---
headers = {
    'Authorization': f'Bearer {DIFY_API_KEY}',
    'Content-Type': 'application/json'
}

# 用于存储所有提取到的 segments 数据的列表
all_segments_data = []

# --- 配置重试策略 ---
# retries=5 表示总共尝试 1（初始请求）+ 5（重试）= 6 次
# backoff_factor=1 表示重试间隔为 1s, 2s, 4s, 8s, 16s...
# status_forcelist 定义当服务器返回这些状态码时强制重试
# allowed_methods 定义哪些 HTTP 方法可以重试（通常GET是安全的）
# respect_retry_after_header=True 会根据响应中的 Retry-After 头部来等待
retries = Retry(
    total=5,  # 最大重试次数（不包括第一次尝试）
    backoff_factor=1,  # 指数退避因子
    status_forcelist=[500, 502, 503, 504],  # 通常重试服务器错误
    allowed_methods=['GET'],  # 仅对 GET 请求进行重试
    respect_retry_after_header=True  # 遵循 Retry-After 头部
)

# 创建一个 HTTPAdapter，并将重试策略挂载到它上面
# pool_connections 和 pool_maxsize 是连接池的设置，可以根据需求调整
adapter = HTTPAdapter(max_retries=retries, pool_connections=10, pool_maxsize=10)

# 创建一个 Session 对象
session = requests.Session()

# 将适配器挂载到 Session 上，用于所有 http:// 和 https:// 请求
session.mount('http://', adapter)
session.mount('https://', adapter)

# 分页参数初始化
page = 1
has_more = True
limit = 50  # 再次确认这个 limit 是否符合 Dify API 的限制

print(f"开始从 {BASE_URL} 提取所有分页的数据 (已启用重试逻辑)...")

while has_more:
    # 构建当前页的查询参数
    params = {
        'page': page,
        'limit': limit
    }

    try:
        print(f"正在获取第 {page} 页，限制 {limit} 条数据...")
        # 使用 session.get 来发送请求，重试逻辑将在这里自动处理
        response = session.get(BASE_URL, headers=headers, params=params)

        # 检查响应状态码。如果重试后仍然是错误状态码，raise_for_status() 会抛出异常
        response.raise_for_status()

        response_data = response.json()

        # 提取当前页的数据
        segments_on_this_page = response_data.get('data', [])

        if not segments_on_this_page:
            print(f"第 {page} 页没有数据，停止获取。")
            break  # 如果当前页没有数据，就跳出循环

        for segment in segments_on_this_page:
            # 提取所需的字段
            extracted_info = {
                'id': segment.get('id'),
                'document_id': segment.get('document_id'),
                'content': segment.get('content'),
                # 将 keywords 列表转换为逗号分隔的字符串，方便在 Excel 中显示
                'keywords': ', '.join(segment.get('keywords', []))
            }
            all_segments_data.append(extracted_info)

        # 检查是否有更多数据
        has_more = response_data.get('has_more', False)
        total_items_fetched = len(all_segments_data)
        total_available = response_data.get('total', '未知')  # 显示总数

        print(f"已成功获取第 {page} 页 ({len(segments_on_this_page)} 条)。")
        print(f"当前已累计获取 {total_items_fetched} 条数据 (总共约 {total_available} 条)。")

        if has_more:
            page += 1  # 准备获取下一页
        else:
            print("所有数据已获取完毕！")

    # 以下异常捕获块，将在重试机制耗尽所有尝试后，如果仍未成功，才会被触发
    except requests.exceptions.HTTPError as http_err:
        print(f"HTTP 错误发生 (多次重试后仍失败): {http_err}")
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")  # 打印错误响应的详细内容
        has_more = False  # 出错时停止循环
    except requests.exceptions.ConnectionError as conn_err:
        # 重连错误 (RemoteDisconnected 等) 在重试器内部处理，
        # 如果这里捕获到，意味着所有重试尝试都失败了。
        print(f"连接错误发生 (多次重试后仍失败): {conn_err}")
        print("请检查网络连接或Dify API是否可达。")
        has_more = False
    except requests.exceptions.Timeout as timeout_err:
        # 超时错误 (同样会在重试器内部处理，这里捕获的是重试耗尽后的最终失败)
        print(f"请求超时 (多次重试后仍失败): {timeout_err}")
        has_more = False
    except requests.exceptions.RequestException as req_err:
        # 捕获其他所有 requests 相关的通用异常
        print(f"发生未知请求错误: {req_err}")
        has_more = False
    except json.JSONDecodeError:
        # 如果响应不是有效的 JSON 格式
        print("响应不是有效的 JSON 格式。原始文本响应:")
        print(response.text)
        has_more = False

# --- 将数据保存到 Excel 文件 ---
if all_segments_data:
    df = pd.DataFrame(all_segments_data)
    excel_file_name = '多轮知识库.xlsx'
    df.to_excel(excel_file_name, index=False)  # index=False 不写入DataFrame的索引
    print(f"\n所有数据已成功保存到 {excel_file_name}")
else:
    print("\n没有获取到任何数据，未生成 Excel 文件。")