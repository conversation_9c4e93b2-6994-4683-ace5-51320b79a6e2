<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="51">
            <item index="0" class="java.lang.String" itemvalue="rsa" />
            <item index="1" class="java.lang.String" itemvalue="h11" />
            <item index="2" class="java.lang.String" itemvalue="pyasn1" />
            <item index="3" class="java.lang.String" itemvalue="sniffio" />
            <item index="4" class="java.lang.String" itemvalue="frozenlist" />
            <item index="5" class="java.lang.String" itemvalue="starlette" />
            <item index="6" class="java.lang.String" itemvalue="anyio" />
            <item index="7" class="java.lang.String" itemvalue="aiomysql" />
            <item index="8" class="java.lang.String" itemvalue="uvicorn" />
            <item index="9" class="java.lang.String" itemvalue="python-jose" />
            <item index="10" class="java.lang.String" itemvalue="annotated-types" />
            <item index="11" class="java.lang.String" itemvalue="pydantic" />
            <item index="12" class="java.lang.String" itemvalue="six" />
            <item index="13" class="java.lang.String" itemvalue="aiohappyeyeballs" />
            <item index="14" class="java.lang.String" itemvalue="ecdsa" />
            <item index="15" class="java.lang.String" itemvalue="python-multipart" />
            <item index="16" class="java.lang.String" itemvalue="click" />
            <item index="17" class="java.lang.String" itemvalue="attrs" />
            <item index="18" class="java.lang.String" itemvalue="fastapi" />
            <item index="19" class="java.lang.String" itemvalue="pydantic_core" />
            <item index="20" class="java.lang.String" itemvalue="colorama" />
            <item index="21" class="java.lang.String" itemvalue="propcache" />
            <item index="22" class="java.lang.String" itemvalue="typing_extensions" />
            <item index="23" class="java.lang.String" itemvalue="python-decouple" />
            <item index="24" class="java.lang.String" itemvalue="aiohttp" />
            <item index="25" class="java.lang.String" itemvalue="multidict" />
            <item index="26" class="java.lang.String" itemvalue="yarl" />
            <item index="27" class="java.lang.String" itemvalue="PyMySQL" />
            <item index="28" class="java.lang.String" itemvalue="aiosignal" />
            <item index="29" class="java.lang.String" itemvalue="idna" />
            <item index="30" class="java.lang.String" itemvalue="langchain-openai" />
            <item index="31" class="java.lang.String" itemvalue="langchain" />
            <item index="32" class="java.lang.String" itemvalue="langchain-core" />
            <item index="33" class="java.lang.String" itemvalue="python-docx" />
            <item index="34" class="java.lang.String" itemvalue="faiss-cpu" />
            <item index="35" class="java.lang.String" itemvalue="nltk" />
            <item index="36" class="java.lang.String" itemvalue="python-dotenv" />
            <item index="37" class="java.lang.String" itemvalue="sentence-transformers" />
            <item index="38" class="java.lang.String" itemvalue="torch" />
            <item index="39" class="java.lang.String" itemvalue="numpy" />
            <item index="40" class="java.lang.String" itemvalue="loguru" />
            <item index="41" class="java.lang.String" itemvalue="tqdm" />
            <item index="42" class="java.lang.String" itemvalue="langchain-community" />
            <item index="43" class="java.lang.String" itemvalue="hnswlib" />
            <item index="44" class="java.lang.String" itemvalue="pypdf2" />
            <item index="45" class="java.lang.String" itemvalue="unstructured" />
            <item index="46" class="java.lang.String" itemvalue="python-magic-bin" />
            <item index="47" class="java.lang.String" itemvalue="tenacity" />
            <item index="48" class="java.lang.String" itemvalue="pandas" />
            <item index="49" class="java.lang.String" itemvalue="flask" />
            <item index="50" class="java.lang.String" itemvalue="Flask" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8NamingInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="N802" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="Stylelint" enabled="true" level="ERROR" enabled_by_default="true" />
  </profile>
</component>