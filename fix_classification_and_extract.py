import pandas as pd
import re
import numpy as np

# 读取Excel文件
try:
    df = pd.read_excel('处理后文件.xlsx')
    print(f"成功读取文件，总行数: {len(df)}")
    
    # 创建修正后的数据框
    df_fixed = df.copy()
    
    # 问题1：检查并修正NaN值的原话
    nan_rows = df[df['原話'].isna()].index.tolist()
    print(f"发现{len(nan_rows)}行原话为空")
    
    # 问题2：检查分类是否合理
    # 创建分类映射表，确保分类一致性
    category_mapping = {
        '售後(糾紛爭議)': '售后(纠纷争议)',
        '客户服务': '客戶服務',
    }
    
    # 应用分类映射
    df_fixed['二級分類'] = df_fixed['二級分類'].replace(category_mapping)
    
    # 问题3：检查原话提取是否准确
    # 计数修正的行数
    fixed_rows = 0
    
    # 遍历每一行
    for idx, row in df.iterrows():
        # 如果原话为空但对话不为空，尝试提取
        if pd.isna(row['原話']) and not pd.isna(row['對話']):
            # 从对话中提取用户说的话
            dialog = str(row['對話'])
            user_messages = re.findall(r'\*\*用戶\*\*\n(.*?)(?=\n\n\*\*系統回復|\n\n\*\*客服|\n\n$)', dialog, re.DOTALL)
            
            if user_messages:
                # 合并用户消息，去除过长的内容
                extracted_text = '\n'.join(user_messages)
                # 如果提取的文本太长，只保留前200个字符
                if len(extracted_text) > 200:
                    extracted_text = extracted_text[:197] + '...'
                df_fixed.at[idx, '原話'] = extracted_text
                fixed_rows += 1
    
    print(f"修正了{fixed_rows}行原话")
    
    # 检查建议是否合理
    empty_suggestions = df[df['建議'].isna()].index.tolist()
    print(f"发现{len(empty_suggestions)}行建议为空")
    
    # 保存修正后的文件
    df_fixed.to_excel('处理后文件_修正版.xlsx', index=False)
    print("已保存修正后的文件：处理后文件_修正版.xlsx")
    
    # 输出一些统计信息
    print("\n分类统计:")
    print(df_fixed['二級分類'].value_counts())
    
except Exception as e:
    print(f"处理文件时出错: {e}") 