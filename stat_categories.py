import json

def main():
    # 加载JSON数据
    with open('1.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 统计各分类数量
    categories = {}
    for item in data:
        cat = item['category']
        categories[cat] = categories.get(cat, 0) + 1
    
    # 输出结果
    print("分类统计结果:")
    for cat, count in sorted(categories.items(), key=lambda x: x[1], reverse=True):
        print(f"{cat}: {count}条")
    
    print(f"\n总记录数: {len(data)}条")

if __name__ == "__main__":
    main() 