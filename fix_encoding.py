import json

# 尝试读取现有的JSON文件并重新保存，确保正确编码
try:
    print("正在读取JSON文件...")
    with open('dialog_classifications.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 打印前几个类别的内容，验证格式
    print("检查分类结果...")
    if data and len(data) > 0:
        first_item = data[0]
        print("第一个对话的分类结果:")
        for classification in first_item.get("classifications", []):
            print(f"类别: {classification.get('category_name', '未知')}")
            if classification.get("evidence_snippets"):
                print(f"证据数量: {len(classification.get('evidence_snippets', []))}")
    
    # 重新保存文件，确保使用UTF-8编码
    print("重新保存JSON文件...")
    with open('fixed_classifications.json', 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    
    print("修复完成! 结果已保存到 fixed_classifications.json")
except Exception as e:
    print(f"处理过程中发生错误: {e}")
    import traceback
    traceback.print_exc() 