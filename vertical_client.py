import httpx
from typing import Dict, Any, Optional, AsyncGenerator
import uuid
import json
import asyncio

class VerticalApiClient:
    """
    Vertical AI API 客户端
    用于与 Vertical AI 的 API 进行交互
    """
    
    def __init__(self, api_key: Optional[str] = None, base_url: str = "https://app.verticalstudio.ai"):
        """
        初始化 Vertical AI API 客户端
        
        参数:
            api_key: API密钥
            base_url: API基础URL
        """
        self.api_key = api_key
        self.base_url = base_url
        self.client = httpx.AsyncClient(
            base_url=base_url,
            headers={"Authorization": f"Bearer {api_key}"} if api_key else {}
        )
    
    async def signup_email(self, email: str, password: str) -> Dict[str, Any]:
        """
        发送注册邮件
        
        参数:
            email: 用户邮箱
            password: 用户密码
            
        返回:
            API响应
        """
        url = f"{self.base_url}/signup-email.data"
        payload = {
            "email": email,
            "password": password
        }
        
        response = await self.client.post(url, data=payload)
        response.raise_for_status()
        return response.json()
    
    async def get_chat_id(self, model_url: str, auth_token: str) -> str:
        """
        获取聊天ID
        
        参数:
            model_url: 模型URL
            auth_token: 认证令牌
            
        返回:
            聊天ID
        """
        # 通常这个方法会向服务器请求一个新的聊天ID
        # 但在这里我们简单地生成一个UUID作为聊天ID
        return str(uuid.uuid4())
    
    async def send_message_stream(self, 
                                 model_url: str, 
                                 auth_token: str, 
                                 chat_id: str, 
                                 messages: list, 
                                 stream: bool = True, 
                                 **kwargs) -> AsyncGenerator[Dict[str, Any], None]:
        """
        发送消息并获取流式响应
        
        参数:
            model_url: 模型URL
            auth_token: 认证令牌
            chat_id: 聊天ID
            messages: 消息列表
            stream: 是否使用流式响应
            **kwargs: 其他参数
            
        返回:
            流式响应生成器
        """
        # 模拟流式响应
        response_text = "这是来自Vertical AI的模拟回复。由于服务器端缺少实际的实现，我们提供这个模拟回复作为示例。"
        
        # 创建一个标准的OpenAI格式响应
        response = {
            "id": f"chatcmpl-{uuid.uuid4()}",
            "object": "chat.completion.chunk",
            "created": int(asyncio.get_event_loop().time()),
            "model": model_url.split("/")[-1].replace(".data", ""),
            "choices": [
                {
                    "index": 0,
                    "delta": {"role": "assistant"},
                    "finish_reason": None
                }
            ]
        }
        
        # 首先发送角色信息
        yield response
        
        # 然后逐字发送内容
        for char in response_text:
            await asyncio.sleep(0.01)  # 模拟网络延迟
            response = {
                "id": f"chatcmpl-{uuid.uuid4()}",
                "object": "chat.completion.chunk",
                "created": int(asyncio.get_event_loop().time()),
                "model": model_url.split("/")[-1].replace(".data", ""),
                "choices": [
                    {
                        "index": 0,
                        "delta": {"content": char},
                        "finish_reason": None
                    }
                ]
            }
            yield response
        
        # 最后发送完成信息
        response = {
            "id": f"chatcmpl-{uuid.uuid4()}",
            "object": "chat.completion.chunk",
            "created": int(asyncio.get_event_loop().time()),
            "model": model_url.split("/")[-1].replace(".data", ""),
            "choices": [
                {
                    "index": 0,
                    "delta": {},
                    "finish_reason": "stop"
                }
            ]
        }
        yield response
    
    async def close(self):
        """关闭HTTP客户端"""
        await self.client.aclose() 