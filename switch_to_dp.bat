@echo on
REM 尝试切换到DP口
echo 正在尝试切换到DP口...

REM 尝试值15（可能是DP口）
echo 尝试值15...
"%~dp0ControlMyMonitor\ControlMyMonitor.exe" /SetValue Primary 60 15
timeout /t 2

REM 如果上面的值不起作用，尝试其他可能的值
echo 尝试值8...
"%~dp0ControlMyMonitor\ControlMyMonitor.exe" /SetValue Primary 60 8
timeout /t 2

echo 尝试值3...
"%~dp0ControlMyMonitor\ControlMyMonitor.exe" /SetValue Primary 60 3
timeout /t 2

echo 尝试值1...
"%~dp0ControlMyMonitor\ControlMyMonitor.exe" /SetValue Primary 60 1
timeout /t 2

echo 尝试值0...
"%~dp0ControlMyMonitor\ControlMyMonitor.exe" /SetValue Primary 60 0
timeout /t 2

echo 尝试完成，请检查显示器是否已切换到DP口
pause 