import pandas as pd
import json
import os

# 设置文件路径
input_file = r"C:\Users\<USER>\Desktop\源文件1.xlsx"
output_file = r"C:\Users\<USER>\Desktop\处理后文件.xlsx"

# 读取Excel文件
try:
    df = pd.read_excel(input_file)
    print(f"成功读取文件，共 {len(df)} 行数据")
except Exception as e:
    print(f"读取文件时出错: {e}")
    exit(1)

# 检查必要的列是否存在
required_columns = ["申訴ID", "對話", "一級分類", "內容"]
missing_columns = [col for col in required_columns if col not in df.columns]
if missing_columns:
    print(f"文件中缺少以下列: {', '.join(missing_columns)}")
    exit(1)

# 创建一个新的DataFrame来存储拆分后的数据
new_rows = []

# 处理每一行数据
for index, row in df.iterrows():
    appeal_id = row["申訴ID"]
    dialogue = row["對話"]
    primary_category = row["一級分類"]
    content = row["內容"]
    
    # 检查内容是否为空
    if pd.isna(content) or content == "":
        # 如果内容为空，保留原行
        new_row = row.to_dict()
        new_rows.append(new_row)
        continue
    
    try:
        # 尝试解析JSON内容
        content_json = json.loads(content)
        
        # 检查JSON格式是否符合预期
        if "classifications" not in content_json:
            print(f"行 {index+2}: JSON格式不符合预期，缺少 'classifications' 字段")
            new_row = row.to_dict()
            new_rows.append(new_row)
            continue
        
        # 获取分类列表
        classifications = content_json["classifications"]
        
        # 如果没有分类，保留原行
        if not classifications:
            new_row = row.to_dict()
            new_rows.append(new_row)
            continue
        
        # 处理每个分类
        for classification in classifications:
            # 获取二级分类和原话
            if "category_name" not in classification or "evidence_snippets" not in classification:
                print(f"行 {index+2}: 分类中缺少必要的字段")
                continue
            
            secondary_category = classification["category_name"]
            evidence_snippets = classification["evidence_snippets"]
            
            # 将原话列表转换为字符串，用换行符分隔
            evidence_text = "\n".join(evidence_snippets) if evidence_snippets else ""
            
            # 创建新行
            new_row = row.to_dict()
            new_row["二級分類"] = secondary_category
            new_row["原話"] = evidence_text
            
            # 添加到新行列表
            new_rows.append(new_row)
    
    except json.JSONDecodeError:
        print(f"行 {index+2}: JSON解析失败")
        new_row = row.to_dict()
        new_rows.append(new_row)
    except Exception as e:
        print(f"行 {index+2}: 处理时出错: {e}")
        new_row = row.to_dict()
        new_rows.append(new_row)

# 创建新的DataFrame
new_df = pd.DataFrame(new_rows)

# 保存到新的Excel文件
try:
    new_df.to_excel(output_file, index=False)
    print(f"处理完成，结果已保存到: {output_file}")
except Exception as e:
    print(f"保存文件时出错: {e}")