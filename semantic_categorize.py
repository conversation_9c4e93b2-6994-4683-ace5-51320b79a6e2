import json

def semantic_categorize(content):
    """
    基于深度语义理解对客服聊天内容进行分类
    完全依靠对内容的理解，不使用关键词匹配或正则表达式
    """
    # 首先清洗一下文本
    content = content.strip()
    
    # 空内容直接归为其他问题
    if not content:
        return "其他綜合問題"
    
    # 根据语义理解分类
    
    # 售后(纠纷争议) - 交易完成后出现的问题，如退款、账号被找回、商品不符等
    if any([
        # 交易后账号被盗回
        "帳號被盜回" in content and "交易" in content,
        # 交易后账号无法使用
        "交易" in content and "無法登入" in content and not "要如何" in content,
        # 退款请求
        "退款" in content and not "如何" in content,
        # 取消交易
        "取消交易" in content and "責任" in content,
        # 交易争议
        "糾紛" in content or "爭議" in content,
        # 商品与描述不符
        "不符" in content and ("商品" in content or "賣場" in content),
        # 卖家不发货或不回应
        "賣家" in content and ("不回應" in content or "不發貨" in content),
        # 延长交易时间
        "延長交易" in content or "延長時間" in content,
        # 退款操作或退款咨询
        "退款給買家" in content
    ]):
        return "售后(纠纷争议)"
    
    # 注册登录 - 纯粹的账号登录问题，不涉及交易后问题
    if any([
        # 账号无法登录且不涉及交易
        "無法登入" in content and "帳號" in content and not "交易" in content,
        # 密码问题
        "密碼" in content and ("錯誤" in content or "忘記" in content) and not "支付密碼" in content,
        # 账号被盗非交易相关
        "帳號被盜" in content and not "交易" in content,
        # 账号安全问题
        "帳號安全" in content or "帳號異常" in content
    ]):
        return "註冊登入"
    
    # 身份/安全验证 - 涉及身份验证、人脸识别、健保卡验证等
    if any([
        # 身份验证问题
        "身分證" in content and "驗證" in content,
        # 人脸识别问题
        "人臉識別" in content or "人臉驗證" in content,
        # 健保卡验证
        "健保卡" in content,
        # 支付密码问题
        "支付密碼" in content,
        # 需要上传证件验证身份
        "上傳" in content and "證件" in content
    ]):
        return "身份/安全驗證"
    
    # 付款购买商品 - 支付过程中的问题
    if any([
        # 付款流程
        "付款" in content and not "退款" in content,
        # 支付问题
        "支付" in content and not "支付密碼" in content,
        # 订单付款后无法联系卖家
        "已付款" in content and "聯繫不到賣家" in content,
        # 开通交易
        "開通交易" in content or "開啟交易" in content,
        # 下单后卖家不回应
        "下單" in content and "賣家" in content and ("不回應" in content or "不回复" in content)
    ]):
        return "付款購買商品"
    
    # 交易环境 - 交易规则、检举违规、平台交易秩序等
    if any([
        # 检举违规
        "檢舉" in content and "違規" in content,
        # 私下交易被举报
        "私下交易" in content,
        # 禁言惩罚
        "禁言" in content,
        # 违反平台规则
        "違反規則" in content,
        # 重复刊登、虚假广告等违规行为举报
        "重複刊登" in content or "虛假廣告" in content,
        # 其他违规行为举报
        "違規" in content and ("舉報" in content or "檢舉" in content)
    ]):
        return "交易環境"
    
    # 发布商品 - 商品上架、刊登规则等问题
    if any([
        # 商品刊登
        "刊登" in content and ("商品" in content or "賣場" in content),
        # 商品上架、下架
        "上架" in content or "下架" in content,
        # 刊登规则
        "刊登規則" in content,
        # 出售商品相关问题
        "出售" in content and "商品" in content and not "權限" in content
    ]):
        return "發佈商品"
    
    # 储值点数 - 点卡购买、储值失败等
    if any([
        # 点卡购买
        "點卡購買" in content or "購買點卡" in content,
        # 储值问题
        "儲值" in content and not "虛擬" in content,
        # 点数不足
        "點數不足" in content,
        # 储值失败
        "儲值失敗" in content
    ]):
        return "儲值點數"
    
    # 操作交互及UI设计 - 平台使用体验、功能查找等
    if any([
        # 功能查找问题
        "找不到" in content and "功能" in content,
        # 界面使用问题
        "界面" in content and "使用" in content,
        # 新旧版本切换
        "新版" in content or "舊版" in content,
        # 操作流程咨询
        "操作" in content and "流程" in content,
        # UI体验问题
        "使用體驗" in content
    ]):
        return "操作交互及UI設計"
    
    # 售前(出售权限) - 申请出售权限、保字会员等
    if any([
        # 保字会员申请
        "保字" in content and "申請" in content,
        # 代储商品销售许可
        "代儲" in content and "出售" in content,
        # 出售权限申请
        "出售權限" in content,
        # 缴纳保证金
        "繳納" in content and "保證金" in content,
        # 5万元保证金
        "5萬" in content and "保證金" in content
    ]):
        return "售前(出售权限)"
    
    # 提取账户款项 - 提现、银行账户等问题
    if any([
        # 提款问题
        "提款" in content,
        # 银行账户关联
        "銀行帳戶" in content,
        # 提现申请
        "提現" in content,
        # 汇款问题
        "匯款" in content
    ]):
        return "提取賬戶款項"

    # 如果以上都不符合，则归为其他综合问题
    # 包括简单咨询、发票信息、操作引导等
    return "其他綜合問題"

def main():
    input_file = '1.json'
    output_file = '1.json'

    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            records = json.load(f)
    except FileNotFoundError:
        print(f"错误：找不到文件 {input_file}")
        return
    except json.JSONDecodeError:
        print(f"错误：无法解析JSON文件 {input_file}")
        return

    if not isinstance(records, list):
        print(f"错误：预期{input_file}中应包含记录列表，但获得了{type(records)}")
        return
        
    updated_records = 0
    for record in records:
        if 'content' in record and isinstance(record['content'], str):
            original_category = record.get('category', '')
            determined_category = semantic_categorize(record['content'])
            record['category'] = determined_category
            if original_category != determined_category:
                updated_records += 1
                # 输出变化详情
                # print(f"内容: {record['content'][:50]}...\n原分类: '{original_category}', 新分类: '{determined_category}'\n---")
        else:
            if 'category' not in record or not record['category']:
                record['category'] = "其他綜合問題"

    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(records, f, ensure_ascii=False, indent=2)
        print(f"成功处理 {len(records)} 条记录。更新了 {updated_records} 个分类。")
        print(f"输出已保存至 {output_file}")
    except IOError:
        print(f"错误：无法写入输出文件 {output_file}")

if __name__ == '__main__':
    main() 