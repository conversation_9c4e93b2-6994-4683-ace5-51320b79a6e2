import pandas as pd

# 读取原始文件和高级修正后的文件
try:
    df_original = pd.read_excel('处理后文件.xlsx')
    df_fixed = pd.read_excel('处理后文件_修正版.xlsx')
    df_advanced = pd.read_excel('处理后文件_高级修正版.xlsx')
    
    print(f"原始文件行数: {len(df_original)}, 修正后文件行数: {len(df_fixed)}, 高级修正后文件行数: {len(df_advanced)}")
    
    # 检查仍然为空的原话
    empty_text = df_advanced[df_advanced['原話'].isna()].index.tolist()
    print(f"\n高级修正后仍有{len(empty_text)}行原话为空")
    
    if empty_text:
        print("\n原话为空的行:")
        for idx in empty_text:
            print(f"行索引: {idx}, 申訴ID: {df_advanced.loc[idx, '申訴ID']}, 二級分類: {df_advanced.loc[idx, '二級分類']}")
    
    # 检查建议是否都已填充
    empty_suggestions = df_advanced[df_advanced['建議'].isna()].index.tolist()
    print(f"\n高级修正后仍有{len(empty_suggestions)}行建议为空")
    
    # 显示新生成的建议示例
    if len(empty_suggestions) == 0:
        # 显示原本为空但现在已填充的建议
        filled_suggestions = [idx for idx in df_fixed[df_fixed['建議'].isna()].index.tolist() if not pd.isna(df_advanced.loc[idx, '建議'])]
        
        print("\n新生成的建议示例:")
        for i, idx in enumerate(filled_suggestions[:5]):  # 只显示前5个
            print(f"行索引: {idx}, 申訴ID: {df_advanced.loc[idx, '申訴ID']}")
            print(f"二級分類: {df_advanced.loc[idx, '二級分類']}")
            print(f"原話: {df_advanced.loc[idx, '原話']}")
            print(f"建议: {df_advanced.loc[idx, '建議']}")
            print("-" * 80)
    
    # 显示高级修正后的几行数据
    print("\n高级修正后的前5行数据:")
    print(df_advanced.head(5)[['申訴ID', '二級分類', '原話', '建議']].to_string())
    
    # 比较原始文件和高级修正后的文件
    modified_rows = 0
    for idx in range(len(df_original)):
        if (df_original.loc[idx, '原話'] != df_advanced.loc[idx, '原話'] and not (pd.isna(df_original.loc[idx, '原話']) and pd.isna(df_advanced.loc[idx, '原話']))) or \
           (df_original.loc[idx, '建議'] != df_advanced.loc[idx, '建議'] and not (pd.isna(df_original.loc[idx, '建議']) and pd.isna(df_advanced.loc[idx, '建議']))):
            modified_rows += 1
    
    print(f"\n总共修改了{modified_rows}行数据")
    
except Exception as e:
    print(f"处理文件时出错: {e}") 