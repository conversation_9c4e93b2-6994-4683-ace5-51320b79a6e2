import requests
import json
import pandas as pd
import time
import concurrent.futures
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("upload_log.txt", encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 请替换以下变量为您的实际值
dataset_id = "2892e0af-48c9-4b14-ba94-9835232b430a"
api_key = "dataset-OFijVX4LxPAn3lsgfVriAdls"
excel_path = Path("D:/dify相关/上传知识/知識庫.xlsx")
max_workers = 5  # 并发数
max_retries = 10  # 最大重试次数

# 存储失败记录
failed_uploads = []

def upload_segment(row):
    """上传单个数据段到Dify API，包含重试逻辑"""
    document_id = row['document_id']
    content = row['content']
    
    # 处理keywords，确保它是一个列表
    keywords = row['keywords']
    if isinstance(keywords, str):
        # 将逗号分隔的字符串转换为列表，并处理可能的空格
        keywords_list = []
        for k in keywords.split('，'):  # 注意这里使用中文逗号分隔
            k = k.strip()
            if k:
                keywords_list.append(k)
        keywords = keywords_list
    elif pd.isna(keywords):
        keywords = []
    
    url = f"https://api.dify.ai/v1/datasets/{dataset_id}/documents/{document_id}/segments"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    data = {
        "segments": [
            {
                "content": content,
                "answer": "",
                "keywords": keywords
            }
        ]
    }
    
    # 重试逻辑
    for attempt in range(max_retries):
        try:
            response = requests.post(url, headers=headers, json=data)
            if response.status_code == 200 or response.status_code == 201:
                logging.info(f"成功上传 document_id: {document_id}")
                return {"success": True, "document_id": document_id, "response": response.json()}
            else:
                logging.warning(f"上传失败 document_id: {document_id}, 状态码: {response.status_code}, 响应: {response.text}")
                # 如果是最后一次尝试，记录失败
                if attempt == max_retries - 1:
                    return {"success": False, "document_id": document_id, "error": f"状态码: {response.status_code}, 响应: {response.text}"}
        except Exception as e:
            logging.warning(f"上传异常 document_id: {document_id}, 异常: {str(e)}")
            # 如果是最后一次尝试，记录失败
            if attempt == max_retries - 1:
                return {"success": False, "document_id": document_id, "error": str(e)}
        
        # 计算下一次重试的等待时间（递增）
        wait_time = 5 * (attempt + 1)
        logging.info(f"等待 {wait_time} 秒后重试 document_id: {document_id}，尝试次数: {attempt+1}/{max_retries}")
        time.sleep(wait_time)
    
    return {"success": False, "document_id": document_id, "error": "达到最大重试次数"}

def process_excel():
    """处理Excel文件并上传数据"""
    try:
        # 读取Excel文件
        logging.info(f"正在读取Excel文件: {excel_path}")
        df = pd.read_excel(excel_path)
        logging.info(f"成功读取Excel文件，共 {len(df)} 条记录")
        
        # 检查必要的列是否存在
        required_columns = ['content', 'document_id', 'keywords']
        for col in required_columns:
            if col not in df.columns:
                logging.error(f"Excel缺少必要的列: {col}")
                return
        
        total = len(df)
        success_count = 0
        failed_count = 0
        
        # 使用线程池进行并发处理
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_row = {executor.submit(upload_segment, row): row for _, row in df.iterrows()}
            
            # 处理完成的任务
            for future in concurrent.futures.as_completed(future_to_row):
                result = future.result()
                if result["success"]:
                    success_count += 1
                else:
                    failed_count += 1
                    failed_uploads.append(result)
                
                # 显示进度
                logging.info(f"进度: {success_count + failed_count}/{total}, 成功: {success_count}, 失败: {failed_count}")
        
        # 保存失败记录
        if failed_uploads:
            with open("failed_uploads.json", "w", encoding="utf-8") as f:
                json.dump(failed_uploads, f, ensure_ascii=False, indent=2)
            logging.info(f"已将 {failed_count} 条失败记录保存到 failed_uploads.json")
        
        logging.info(f"任务完成! 总计: {total}, 成功: {success_count}, 失败: {failed_count}")
        
    except Exception as e:
        logging.error(f"处理Excel文件时发生错误: {str(e)}")

if __name__ == "__main__":
    logging.info("开始上传知识库到Dify")
    process_excel()
    logging.info("任务执行完毕")