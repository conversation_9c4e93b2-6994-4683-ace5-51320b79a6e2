import pandas as pd
import re

# 读取Excel文件
try:
    df = pd.read_excel('处理后文件.xlsx')
    print(f"成功读取文件，总行数: {len(df)}")
    
    # 创建修正后的数据框
    df_fixed = df.copy()
    
    # 标准分类列表
    standard_categories = [
        '售前(出售权限)',
        '售中(交易问题)',
        '售后(纠纷争议)',
        '註冊登入',
        '身份/安全驗證',
        '發佈商品',
        '儲值點數',
        '提取賬戶款項',
        '付款購買商品',
        '交易安全',
        '平台穩定性',
        '交易環境',
        '操作交互及UI設計',
        '客戶服務',
        '其他綜合問題'  # 保留原有的其他综合问题分类
    ]
    
    # 创建分类映射表，统一分类名称
    category_mapping = {
        '售後(糾紛爭議)': '售后(纠纷争议)',
        '客户服务': '客戶服務'
    }
    
    # 应用分类映射
    df_fixed['二級分類'] = df_fixed['二級分類'].replace(category_mapping)
    
    # 检查并统计非标准分类
    non_standard_categories = []
    for idx, row in df_fixed.iterrows():
        category = row['二級分類']
        if category not in standard_categories:
            non_standard_categories.append({
                '行索引': idx,
                '申訴ID': row['申訴ID'],
                '原分类': category
            })
    
    print(f"发现{len(non_standard_categories)}个非标准分类")
    for item in non_standard_categories:
        print(f"行索引: {item['行索引']}, 申訴ID: {item['申訴ID']}, 原分类: {item['原分类']}")
    
    # 根据对话内容和原话重新分类
    reclassified_count = 0
    
    for idx, row in df_fixed.iterrows():
        # 如果当前分类不在标准列表中，需要重新分类
        if row['二級分類'] not in standard_categories:
            # 获取对话内容和原话
            dialog = str(row['對話']) if not pd.isna(row['對話']) else ""
            original_text = str(row['原話']) if not pd.isna(row['原話']) else ""
            combined_text = dialog + " " + original_text
            
            # 基于关键词进行分类
            new_category = classify_by_keywords(combined_text)
            
            # 应用新分类
            df_fixed.at[idx, '二級分類'] = new_category
            reclassified_count += 1
            print(f"行索引: {idx}, 申訴ID: {row['申訴ID']}, 原分类: {row['二級分類']} -> 新分类: {new_category}")
    
    print(f"重新分类了{reclassified_count}行数据")
    
    # 保存修正后的文件
    df_fixed.to_excel('处理后文件_分类修正版.xlsx', index=False)
    print("已保存修正后的文件：处理后文件_分类修正版.xlsx")
    
    # 输出分类统计
    print("\n修正后的分类统计:")
    print(df_fixed['二級分類'].value_counts())
    
except Exception as e:
    print(f"处理文件时出错: {e}")

def classify_by_keywords(text):
    """根据文本内容中的关键词进行分类"""
    text = text.lower()
    
    # 售前(出售权限)
    if any(keyword in text for keyword in ['申请保字', '出售权限', '卖家资格', '申请出售', '保字会员', '出售资格']):
        return '售前(出售权限)'
    
    # 售中(交易问题)
    if any(keyword in text for keyword in ['交易中', '交易时间', '延长交易', '交易流程', '交易码', '交易状态', '交易进行中']):
        return '售中(交易问题)'
    
    # 售后(纠纷争议)
    if any(keyword in text for keyword in ['退款', '纠纷', '争议', '投诉', '不符合', '取消交易', '退回', '售后', '不满意', '不实广告']):
        return '售后(纠纷争议)'
    
    # 註冊登入
    if any(keyword in text for keyword in ['注册', '登录', '登入', '账号登录', '无法登入', '账号注册']):
        return '註冊登入'
    
    # 身份/安全驗證
    if any(keyword in text for keyword in ['验证', '身份', '验证码', '安全验证', '手机验证', '身份证', '健保卡', '证件']):
        return '身份/安全驗證'
    
    # 發佈商品
    if any(keyword in text for keyword in ['发布商品', '上架', '刊登', '发布', '商品上架', '商品刊登']):
        return '發佈商品'
    
    # 儲值點數
    if any(keyword in text for keyword in ['储值', '点数', '充值', '储值点数', '点卡', 'mycard']):
        return '儲值點數'
    
    # 提取賬戶款項
    if any(keyword in text for keyword in ['提现', '提款', '提取', '账户余额', '取款', '提取款项']):
        return '提取賬戶款項'
    
    # 付款購買商品
    if any(keyword in text for keyword in ['购买', '付款', '支付', '买家', '购买商品', '付款方式', '支付问题']):
        return '付款購買商品'
    
    # 交易安全
    if any(keyword in text for keyword in ['安全', '风险', '诈骗', '盗号', '交易安全', '账号安全', '私下交易']):
        return '交易安全'
    
    # 平台穩定性
    if any(keyword in text for keyword in ['稳定', '崩溃', '卡顿', '平台问题', '系统问题', '网站问题', '无法访问']):
        return '平台穩定性'
    
    # 交易環境
    if any(keyword in text for keyword in ['交易环境', '交易规则', '交易氛围', '交易体验', '市场环境', '瀏覽量', '推薦']):
        return '交易環境'
    
    # 操作交互及UI設計
    if any(keyword in text for keyword in ['界面', 'ui', '操作', '交互', '设计', '功能', '使用体验', '网站设计']):
        return '操作交互及UI設計'
    
    # 客戶服務
    if any(keyword in text for keyword in ['客服', '服务', '咨询', '回复', '客户服务', '服务态度', '辛苦了']):
        return '客戶服務'
    
    # 如果无法根据关键词分类，则归为其他综合问题
    return '其他綜合問題' 