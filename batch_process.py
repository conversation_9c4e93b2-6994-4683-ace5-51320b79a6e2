import pandas as pd
import requests
import json
import time
import os
import asyncio
import aiohttp
from tqdm import tqdm
from datetime import datetime
import random
import nest_asyncio

# 应用nest_asyncio来解决嵌套事件循环问题
nest_asyncio.apply()

# 配置信息
CONFIG = [
    {
        "url": "https://api.x.ai/v1/chat/completions",
        "model": "grok-3-latest",
        "api_key": "************************************************************************************",
        "concurrency": 20  # 并发数
    },
    {
        "url": "https://api.x.ai/v1/chat/completions",
        "model": "grok-3-latest",
        "api_key": "************************************************************************************",
        "concurrency": 20  # 并发数
    },
    {
        "url": "https://api.x.ai/v1/chat/completions",
        "model": "grok-3-latest",
        "api_key": "************************************************************************************",
        "concurrency": 20  # 并发数
    }
]

# 输入和输出文件
INPUT_FILE = "批量測試問答/1.xlsx"
OUTPUT_FILE = f"results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

# 保存频率（每处理多少条数据保存一次）
SAVE_FREQUENCY = 100

# 请求超时时间（秒）
TIMEOUT = 60

# 系统提示词
SYSTEM_PROMPT = """
你是一个专业的问题匹配助手，你需要从以下问题列表中，找到与用户的问题最匹配的问题（若有多个符合，请返回最匹配的问题）。

语义相似度需95%以上，才可匹配。若你认为语义相似度低于95%，请返回"未匹配"。

问题列表：
1.【續刊】相關問題
2.591的點數是否永久有效？
3.591的討論區在哪裡呢？
4.591會開立發票嗎？
5.591會員帳號是什麼
6.591開通店鋪要付費?
7.591可以刊登海外物業嗎？
8.591收費方案
9.安卓手機如何儲值
10.保護電話的分機碼錯了，怎麼辦？
11.保護電話無法撥通
12.保護電話需要另外收費嗎
13.不會用591，可以幫我代刊嗎？
14.不想收到簡訊要如何操作
15.查看賬號餘額
16.查詢我的發票
17.產權登記是什麼？已辦、未辦該如何選擇？
18.成交下架
19.成交下架自助功能使用教學
20.出售廣告單價是如何計算的
21.出售套餐有區分住家與商用類嗎
22.出售物件可以刊登預售屋嗎
23.出售物件置頂加值服務
24.出售新手套餐相關問題
25.出租車位如何收費
26.出租單筆加值服務介紹
27.出租套餐更換次數的意思
28.出租套餐可以刊登出售嗎
29.出租套餐如何更換物件
30.出租套餐物件怎麼更新
31.出租退點
32.出租住家套餐加值服務
33.出租住宅的建物面積是如何計算的，為什麼不能改
34.儲值錯誤怎麼辦
35.儲值方式有哪些呢
36.儲值加值幣有發票嗎
37.儲值了為什麼沒有點數
38.儲值入帳時間及手續費
39.儲值賬號是多少
40.代理人可以收取服務費嗎
41.地圖找房找不到我的物件
42.地下樓如何填寫
43.地址是無誤的，權狀書未在身邊如何核實
44.登入時提示*非法請求*
45.登入時提示賬號不存在
46.登入提示帳號異常
47.點數可以用來做什麼
48.點數與台幣比例是多少？
49.店舖的行動電話可否隱藏?
50.店鋪開通後多久才能顯示
51.電話號碼可以用室內電話嗎
52.電腦可以看到房聊嗎
53.頂樓加蓋如何填寫
54.定時更新2.0 常見問題
55.定時更新2.0 使用教學
56.定時更新時間有誤
57.獨立套房和分租套房的區別是什麼？
58.發佈新建案動態有什麼規定
59.發票寄送規則
60.發票開的品項
61.發票可以分開開立嗎
62.發票可以提前寄送嗎
63.發票是電子發票嗎
64.發票填寫錯誤如何改開
65.發票問題
66.房貸業務員身份如何改為其他身份
67.房東怎麼看有多少人收藏
68.房聊無法傳出訊息
69.房聊有新消息時，手機會通知嗎？
70.房聊中不知道房客問哪間怎麼辦？
71.房屋地址的巷弄號只能填寫數字嗎？
72.房屋實際交易中遇到問題，如何處理？
73.房屋問答是否會被公開
74.分租套房/雅房的其他房屋資料，App會顯示嗎？
75.分租套房/雅房如何刪除房間一
76.分租套房或雅房可以一則廣告刊登多間嗎
77.剛購買套餐，卻沒有點數了
78.格局如何填寫
79.格局圖可以修改嗎
80.更多照片問題
81.更新APP後房聊內容消失了
82.公設比怎麼算？
83.公司帳號，頭像可以用團體照嗎？
84.公司賬號也要用行動電話註冊嗎
85.購買加值服務後所有套餐物件均能享用嗎
86.購買了【精選競價】能修改廣告資料嗎？
87.購買了加值服務未顯示
88.購買蘋果點數包你們會開發票嗎？
89.固定電話是指室內電話嗎
90.關注的社區不見了
91.廣告標題最多可以填幾個字？
92.廣告成交後還要付服務費嗎？
93.廣告關閉後仍接到房客電話
94.廣告過期了，如何聯絡屋主
95.廣告或套餐的到期時間如何計算？
96.廣告刊登錯誤
97.廣告刊登期間，可以升級廣告方案嗎？
98.廣告刊登期間，可以升級為黃金曝光方案嗎
99.廣告刊登者的聯絡電話無人接聽怎麼辦？
100.廣告可以隨時下架嗎
101.廣告可以填寫多個行動電話嗎
102.廣告可以在多個市區都顯示嗎
103.廣告可以只顯示給房客，不讓仲介看到嗎
104.廣告聯絡電話一定要填寫註冊電話嗎
105.廣告什麼時候開始計算天數
106.廣告無法成功開啟
107.廣告無法開啟
108.廣告無法修改成功
109.廣告資料填寫完後無法保存送出
110.廣告資料有核實過，為什麼重新刊登還要核實？
111.過期物件可以變更狀態為已成交嗎
112.還未獲得證書可以刊登嗎
113.黃金曝光方案收費標準與服務說明
114.會員約定條款修訂通知無法關閉
115.獲取繳費代碼
116.獲取繳費代碼自助功能使用教學
117.加值服務的時間如何計算
118.加值服務購買錯誤，申請退還點數
119.加值服務收費標準
120.建案電話有異常
121.建案動態審核不通過怎麼辦
122.建案廣告電話可以留行動電話嗎
123.建案審核未通過
124.建案暫時關閉後如何重新開啟
125.繳費後廣告什麼時候能開啟？
126.接到客服電話說我不符合網站規則
127.接到諮詢他人物件的電話
128.結案是完銷的意思嗎
129.解除設備驗證
130.金牌專家加值服務常見問題
131.精選推薦
132.舊廣告再次開啟vr還在嗎
133.舊物件重新開啟時，收藏、瀏覽人數及房客問答會保留嗎？
134.開啟物件
135.開啟物件自助功能使用教學
136.開通店鋪有什麼好處
137.刊登廠房如何收費
138.刊登出售收費標準
139.刊登出租商用如何收費
140.刊登出租住宅收費標準
141.刊登錯誤，需取消
142.刊登錯誤需取消
143.刊登的廣告被客服關閉
144.刊登店面頂讓如何收費
145.刊登廣告一定要上傳照片嗎？
146.刊登建案資訊有什麼規定？
147.刊登求租/ 求購廣告有什麼規定？
148.刊登時提示建案名重複
149.刊登土地如何收費
150.刊登整棟如何填寫
151.刊登之後還能修改照片嗎？
152.刊登中途可以更換物件嗎
153.刊登中途升級廣告方案，時間如何計算？
154.刊登租售房屋資訊有什麼規定？
155.可以把街道名隱藏嗎
156.可以不填巷嗎
157.可以查看物件被更新了幾次嗎
158.可以多個設備同時登入使用賬號嗎
159.可以複製一筆廣告嗎
160.可以關閉競價窗口嗎
161.可以將點數轉到其他賬號嗎？
162.可以將刊登的物件都放在開啟中的物件中嗎
163.可以將瀏覽人數歸零重新計算嗎
164.可以將圖片放在影片前面嗎？
165.可以將一筆廣告的VR加至另一筆廣告嗎
166.可以刊登廣告牌/墻面嗎
167.可以先保存資料，之後刊登嗎
168.可以隱藏門號嗎
169.可以只頂讓設備嗎
170.客服電話佔線
171.萊爾富便利超商儲值
172.聯絡人工客服
173.列印發票副本
174.留言被刪除可以恢復嗎
175.瀏覽人數如何看
176.瀏覽人數有誤
177.沒顯示聯絡電話
178.沒有發佈房屋是什麼意思
179.沒有固定電話怎麼辦？
180.沒有接到電話，但會顯示"X小時"內有人透過電話諮詢了此房屋"
181.沒有上傳權狀，可以刊登嗎？
182.沒有社區名稱要如何填寫？
183.門號未顯示之幾的部分
184.母帳號的套餐可以分配給子帳號嗎
185.母帳號可以管理子帳號嗎
186.母帳號可以設定幾個子帳號
187.母帳號有開通店鋪，子帳號可以管理嗎
188.暱稱如何設定/修改？
189.蘋果點數退款
190.蘋果手機如何儲值
191.普通方案是只有電腦看得到嗎？
192.普通刊登方案和VIP的差別
193.切換為仲介身份後只出現代理人的選項
194.清空價格波動
195.清空價格波動自助功能使用教學
196.請問591網站支持使用英文版的嗎？
197.請問591轉接電話可以設為手機號碼嗎
198.請問出租住宅類和商用類的差別
199.請問刊登廣告有分商用跟住家的嗎?
200.請問刊登在中古屋的預售屋，屋齡如何填寫？
201.請問留言問答，顯示"遊客"是什麼意思
202.請問如何關閉在線諮詢
203.請問如何推薦到店鋪
204.請問影片上傳後多久會審核
205.請問VR是免費嗎
206.取消【金牌專家】加值服務的自動續購
207.取消【精選競價】加值服務的自動續購
208.取消【置頂】加值服務的自動續購
209.取消出售加值服務
210.取消出售類型交易
211.取消出售套餐
212.取消出租加值服務自助功能使用教學
213.取消出租套餐自助功能使用教學
214.取消出租物件自助功能使用教學
215.取消加值服務的自動續購
216.全家便利商店儲值
217.權狀上傳錯誤怎麼辦
218.權狀上傳是公開的嗎
219.權狀書需拍到哪些內容
220.如何按地圖找房
221.如何才能入選優選好屋
222.如何操作成交下架
223.如何查看、回覆或刪除留言訊息？
224.如何查看/設定定時更新
225.如何查看保護電話分機號
226.如何查看點數/刊登明細？
227.如何查看訪客的電話
228.如何查看廣告是否續刊成功
229.如何查看會員基本資料
230.如何查看建案廣告是否刊登成功
231.如何查看歷史瀏覽記錄
232.如何查看實價登錄
233.如何查看收藏的物件/我的最愛
234.如何查看未曝光的物件資料？
235.如何查看賬號剩餘點數
236.如何查看仲介職業認證資料是否通過？
237.如何查詢刊登到期時間
238.如何查詢套餐到期時間與剩餘筆數
239.如何查找經紀人
240.如何登出賬號
241.如何登入帳號
242.如何調整照片順序
243.如何訂閱/取消訂閱電子報？
244.如何返回會員中心
245.如何更換房屋照片？
246.如何更換封面照片
247.如何購買加值服務
248.如何購買套餐
249.如何關閉店鋪
250.如何關閉廣告彈窗
251.如何關閉建案
252.如何關閉推播功能
253.如何關閉找房廣告
254.如何關閉Line聯絡功能？
255.如何換頭像
256.如何繪製格局圖
257.如何將舊廣告的照片上傳到新廣告中？
258.如何進行職業認證
259.如何開立統編發票
260.如何開啟房屋廣告？
261.如何開啟廣告？
262.如何開啟Line聯絡方式？
263.如何開通店鋪
264.如何開通VR功能
265.如何刊登頂讓廣告
266.如何刊登法拍屋
267.如何刊登建案
268.如何聯絡刊登者
269.如何列印發票複本
270.如何留言
271.如何批量更新
272.如何啟用/取消保護電話?
273.如何清空建案動態
274.如何清空瀏覽器cookie？
275.如何取消職業認證
276.如何確定我已經成功購買套餐了呢？
277.如何刪除房聊對話
278.如何刪除格局圖
279.如何刪除廣告照片
280.如何刪除留言？
281.如何刪除物件
282.如何刪除子帳號
283.如何上傳大頭照
284.如何上傳房屋照片
285.如何上傳權狀書
286.如何上傳影片？
287.如何上傳LINE QRcode
288.如何上傳Line網址？
289.如何設定價格波動？
290.如何設定免服務費?
291.如何設定屋主聲明，仲介勿擾
292.如何申請補寄發票
293.如何申請子帳號
294.如何申訴
295.如何升級到超級VIP
296.如何升級VIP
297.如何使用單筆方案開啟廣告？
298.如何使用套餐方案開啟廣告？
299.如何使用信用卡儲值（免手續費）
300.如何手動更新排序
301.如何手動輸入社區名稱
302.如何搜尋已成交的物件
303.如何縮小圖片
304.如何索取紙本發票
305.如何提升廣告曝光效果？
306.如何添加"榜"的標籤
307.如何填寫出租樓層
308.如何填寫廣告資料？
309.如何填寫退現金的資料/何時到賬
310.如何填寫屋齡
311.如何退費？
312.如何下載591APP
313.如何修改【開伙】【車位】【養寵】【電梯】等廣告資料？
314.如何修改從業年限？
315.如何修改店鋪資料？
316.如何修改頂讓廣告資料？
317.如何修改訂閱條件
318.如何修改發票資料？
319.如何修改房屋地址？
320.如何修改房屋類型/法定用途？
321.如何修改房屋樓層？
322.如何修改公司資料
323.如何修改固定電話
324.如何修改廣告的聯絡資料？
325.如何修改廣告資料
326.如何修改會員身份
327.如何修改會員帳號
328.如何修改競價價格
329.如何修改可遷入日
330.如何修改新建案
331.如何修改新建案動態
332.如何修改姓名
333.如何修改已成交的物件？
334.如何修改仲介職業認證資料？
335.如何修改註冊信箱
336.如何修改註冊行動電話
337.如何修改註冊資料
338.如何修改子帳號
339.如何修改VR
340.如何續刊廣告
341.如何旋轉照片？
342.如何隱藏地址門牌號?
343.如何有效提高廣告曝光量？
344.如何預覽廣告
345.如何在會員中心查看建案廣告
346.如何在刊登廣告時取消勾選自動續費？
347.如何在你們平台購買建案廣告
348.如何暫時關閉物件
349.如何找房
350.如何註冊會員？
351.如何註銷賬號？
352.入選<必看好屋>有什麼條件？
353.刪除個資
354.上傳的頭像什麼時候會顯示呢？
355.上傳房屋照片有什麼規定
356.上傳了影片為何沒有顯示出來
357.上傳權狀後，如果核實通過會通知嗎？
358.上傳影片有什麼規定
359.上傳VR後多久會進行審核
360.社區實價登錄資料有誤
361.申訴的問題幾時會回覆
362.身份證字號被認證過無法再認證
363.什麼是保護電話？隱藏了有什麼作用？
364.什麼是定時更新
365.什麼是獨立套房？
366.什麼是房屋權狀坪數？
367.什麼是房屋權狀書？
368.什麼是分租套房？
369.什麼是加急標籤
370.什麼是競價
371.什麼是套餐
372.什麼是土地坪數？
373.什麼是新建案電話回撥功能
374.什麼是新建案動態
375.什麼是行動版精選推薦
376.什麼是行動版置頂
377.什麼是整層住家？
378.什麼是子母帳號
379.什麼是租賃住宅市場管理條例？
380.升級VIP多少錢
381.使用手機App可以刊登套餐筆數嗎？
382.使用套餐方案刊登廣告
383.是否繳費成功
384.是否可用網路銀行轉帳?
385.是否有提供手機和固定電話付費
386.收不到驗證碼，怎麼辦？
387.收到簡訊說我的廣告資訊有誤
388.收到簡訊說我物件資料不符刊登規則
389.收到簡訊要求加line好友
390.收到競價的簡訊是什麼意思
391.收費標準裡面的價格是否含稅呢？
392.手機版如何查詢物件編號
393.手機看不到房聊內容
394.手機如何搜尋頂讓廣告
395.手機APP可以刊登建案嗎
396.手機APP可以上傳房屋權狀影本嗎
397.搜尋不到我的廣告
398.搜尋不到我的廣告
399.搜尋列表無法顯示封面
400.套餐到期前一天刊登，會有多久刊登時間
401.套餐的物件要如何暫時關閉呢？
402.套餐購買錯誤，申請退還點數
403.套餐物件可以升級VIP嗎
404.套餐物件屬於什麼方案？
405.套餐延期
406.套餐要在什麼時間內用完？
407.提供建議
408.提示【物件已過期/未發佈/不存在】是什麼意思？
409.提示email賬號錯誤
410.填寫公司資料時加盟店沒有我要填寫的選項
411.填寫了社區名稱，但未連結到該社區
412.同個廣告有多個門號如何填寫
413.投訴惡劣房東
414.土地沒有門牌怎麼辦？
415.推薦到店鋪是什麼
416.忘記密碼
417.忘記賬號
418.未更新APP是否無法使用房聊功能
419.未開啟照常計費嗎
420.未開通店鋪也可以付費PO物件嗎?
421.未匹配到社區名稱
422.未確定新任職公司的仲介，仲介公司要怎樣填寫？
423.未收到發票
424.為何電腦版可看到格局圖，手機版不行
425.為何我的店鋪會自動關閉？
426.為何無法儲值點數?
427.為何已選擇仲介勿擾還是有仲介來電
428.為什麼標題顏色不是黑色
429.為什麼不能開通VR
430.為什麼儲值被多收30%手續費
431.為什麼剛刊登的新建案，查看不到？
432.為什麼會提示"點數不足"？
433.為什麼搜尋我的VIP物件沒有置頂
434.為什麼我的刊登、修改功能被限制？
435.為什麼我的物件電話不是我的手機
436.為什麼我改了身份，廣告裡的身份還是沒變呢？
437.為什麼物件已收藏，但沒有查看到呢？
438.為什麼要上傳建物權狀
439.為什麼要選是否已辦產權登記
440.為什麼影片無法通過審核
441.為什麼證書上傳失敗
442.問答管理在哪
443.我的廣告是全台灣都看的到嗎？
444.我的物件一天可以更新排序幾次？
445.我的營業員證號有誤如何修改
446.我的照片被客服刪除了
447.我可以上傳多少張房屋照片？
448.我是仲介，為什麼我的廣告沒有大頭貼
449.我要檢舉
450.我要修改出售物件
451.我有收到房聊邀約的簡訊，該如何處理？
452.屋況說明的格式會亂掉
453.屋齡不滿一年怎麼辦
454.屋主留的電話是錯的
455.屋主只有591電話
456.無法登入會員
457.無法更新物件
458.無法刊登/修改新建案
459.無法上傳房屋照片怎麼辦
460.無法上傳頭像
461.無法收到推播
462.無法修改產權登記
463.無法註冊會員
464.物件不小心刪除了，如何恢復？
465.物件地圖定位有誤，如何修改？
466.物件過期後再次開啟是不是算一筆新的物件
467.物件刊登流程
468.物件下架后時間可以保留嗎
469.誤點成交下架怎麼辦
470.系統並沒有自動更新
471.系統提示"地址無法核實"
472.系統有異常
473.下架後剩餘時間可轉換成點數嗎?
474.現況特色描述填寫注意事項
475.想預約看屋
476.新建案查詢完整的來電號碼
477.新建案可以代刊嗎？
478.新建案如何收費
479.新建案圖片上傳有什麼規定？
480.新建案諮詢電話可以留手機嗎
481.信用卡儲值遇到問題
482.信用卡扣款有誤
483.信用卡只能用國泰嗎？
484.行動電話/Email被註冊過怎麼辦？
485.修改房屋廣告資料
486.修改會員資料
487.需上傳什麼資料核實房屋登記資訊
488.要用套餐刊登的，系統自動幫我刊登，扣了我點數
489.頁面被智慧客服擋住，影響使用
490.一個賬號可以同時認證多個職業認證嗎
491.一支手機可以註冊幾個賬號
492.已按要求修改建案資料，麻煩開啟建案
493.已經更改姓名,但系統沒更改!
494.已聯絡蘋果退款
495.已上傳權狀書，請核實
496.已設置保護電話，若房客來電，如何得知房客電話？
497.已有儲值，為什麼開啟廣告時仍需選擇刊登方案？
498.銀行臨櫃匯款儲值流程
499.熒幕字體太小
500.影片上傳有限制時長和檔案大小嗎
501.用手機看不到影片
502.用影片就不能使用VR功能了嗎
503.郵局儲值流程
504.有591未接來電
505.有兩個車位如何填寫
506.有沒有長期刊登的優惠方案?
507.有限定修改次數嗎
508.又有住家，又有辦公，如何刊登
509.預覽時沒有開啟保護電話 / 顯示照片描述
510.預售屋沒有門號如何刊登
511.原本是出租可以改成出售嗎？
512.在線諮詢有什麼作用
513.在修改廣告資料頁面找不到要修改的欄位
514.暫時關閉
515.暫時關閉操作不成功
516.暫時關閉的廣告再次開啟需要付費嗎
517.暫時關閉時間還是照常計算嗎
518.暫時關閉自助功能使用教學
519.怎麼改成房貸業務員
520.怎麼刪除YAHOO奇摩網頁上有關591的刊登資料？
521.怎樣才能出現在經紀人列表？
522.帳號為什麼會被停權？
523.找不到路名怎麼辦
524.照片下的文字為何沒有出現
525.照片已上傳至591email，何時會上傳
526.職業認證需上傳什麼資料
527.職業認證有什麼用
528.職業認證資料何時會審核
529.仲介可以刊登新建案嗎
530.仲介認證個人頭像的大小比例是多少
531.仲介身份，如果自己/幫親友出租房子，可否用屋主/代理人身份刊登？
532.仲介身份無法更改成屋主？
533.仲介與仲介公司身份有什麼區別？
534.仲介職業認證的個人頭像審核標準是什麼？
535.仲介職業認證未通過，如何修改？
536.註冊需要費用嗎
537.註冊要填寫真實姓名嗎
538.註冊一定要台灣手機嗎
539.轉人工
540.自助功能常見問題
541.自助功能服務介紹
542.自助功能使用教學
543.子帳號被刪除後，物件還在嗎？
544.子帳號刊登廣告如何扣費？
545.子帳號可以申請仲介職業認證/上傳個人頭像嗎？
546.子帳號如何登入591會員中心？
547.子帳號是否可以看到母帳號的剩餘點數和套餐明細
548.子帳號忘記密碼怎麼辦
549.租售多個樓層要怎麼寫？
550.租屋可以上傳影片嗎
551.租屋契約問題及契約範例下載
552.最短租期如何填寫
553.最新網站活動
554.Adobe Flash Player被封鎖
555.APP沒看到主建物欄位
556.APP如何上傳影片?
557.APP如何使用FB登入
558.APP如何修改密碼
559.APP如何註冊會員
560.APP要如何更新
561.ATM儲值流程
562.FB如何登入
563.FB如何註冊
564.Google/Apple點數包儲值
565.Google點數退款
566.OK便利超商儲值
567.VIP方案收費標準與服務說明
"""

# 用户提示词模板
USER_PROMPT_TEMPLATE = "请根据以下内容提供回复：{}"

# 每个API请求的最大重试次数
MAX_RETRIES = 3

# 重试延迟（秒）
RETRY_DELAY = 2

# 為每個模型創建信号量
semaphores = {}
for config in CONFIG:
    semaphores[config["model"]] = asyncio.Semaphore(config["concurrency"])

# 创建一个全局的aiohttp会话，避免重复创建
session = None

async def setup_session():
    global session
    if session is None or session.closed:
        session = aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=False))
    return session

async def cleanup_session():
    global session
    if session and not session.closed:
        await session.close()

async def call_api_with_model_switching(prompt, index, original_config_index=None):
    """
    调用API获取回复，失败后尝试切换模型重试
    
    参数:
        prompt: 用户提示词
        index: 数据行索引
        original_config_index: 原始配置索引，用于记录第一次尝试的模型
    
    返回:
        API响应结果
    """
    # 获取会话
    current_session = await setup_session()
    
    # 如果是首次调用，使用轮询分配的模型
    if original_config_index is None:
        config_index = index % len(CONFIG)
        original_config_index = config_index
    else:
        config_index = original_config_index
    
    config = CONFIG[config_index]
    
    # 尝试使用当前模型
    result = await try_call_api(current_session, prompt, config, index)
    
    # 如果当前模型失败，尝试其他模型
    if not result["success"]:
        # 记录原始错误
        original_error = result["error"]
        
        # 尝试其他模型
        used_models = {config_index}
        for _ in range(len(CONFIG) - 1):  # 最多尝试所有其他模型
            # 随机选择一个未使用过的模型
            available_indices = [i for i in range(len(CONFIG)) if i not in used_models]
            if not available_indices:
                break
                
            next_config_index = random.choice(available_indices)
            used_models.add(next_config_index)
            
            next_config = CONFIG[next_config_index]
            print(f"行 {index+1} 使用模型 {config['model']} 失败，尝试切换到 {next_config['model']}...")
            
            # 尝试新模型
            new_result = await try_call_api(current_session, prompt, next_config, index)
            
            # 如果成功，返回结果
            if new_result["success"]:
                return new_result
        
        # 如果所有模型都失败，返回原始错误
        result["error"] = f"所有模型都失败。原始错误: {original_error}"
        print(f"行 {index+1} 所有模型都失败，跳过此行")
    
    return result

async def try_call_api(current_session, prompt, config, index):
    """尝试调用特定模型的API"""
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {config['api_key']}"
    }
    
    # 构建消息列表，包含系统提示词和用户提示词
    messages = [
        {"role": "system", "content": SYSTEM_PROMPT},
        {"role": "user", "content": prompt}
    ]
    
    payload = {
        "model": config["model"],
        "messages": messages,
        "temperature": 0.3,
        "max_tokens": 1000
    }
    
    result = {
        "model": config["model"],
        "success": False,
        "content": "",
        "error": "",
        "prompt_tokens": 0,
        "completion_tokens": 0,
        "total_tokens": 0,
        "index": index  # 保存原始索引
    }
    
    semaphore = semaphores[config["model"]]
    retries = 0
    
    while retries < MAX_RETRIES:
        try:
            async with semaphore:
                async with current_session.post(
                    config["url"], 
                    headers=headers, 
                    json=payload, 
                    timeout=TIMEOUT
                ) as response:
                    response_json = await response.json()
                    
                    if response.status == 200 and "choices" in response_json and len(response_json["choices"]) > 0:
                        message = response_json["choices"][0].get("message", {})
                        content = message.get("content", "")
                        
                        result["success"] = True
                        result["content"] = content
                        
                        # 添加成功信息打印
                        print(f"行 {index+1} 模型 {config['model']} 请求成功!")
                        
                        if "usage" in response_json:
                            usage = response_json["usage"]
                            result["prompt_tokens"] = usage.get("prompt_tokens", 0)
                            result["completion_tokens"] = usage.get("completion_tokens", 0)
                            result["total_tokens"] = usage.get("total_tokens", 0)
                        
                        return result
                    else:
                        error = f"API错误: {response.status} - {json.dumps(response_json)}"
                        result["error"] = error
                        
                        # 如果是限流或服务器错误，重试
                        if response.status in [429, 500, 502, 503, 504]:
                            retries += 1
                            if retries < MAX_RETRIES:
                                print(f"行 {index+1} 模型 {config['model']} 请求失败 (状态码: {response.status})，第 {retries} 次重试...")
                                await asyncio.sleep(RETRY_DELAY * (2 ** retries))  # 指数退避
                                continue
                            else:
                                print(f"行 {index+1} 模型 {config['model']} 重试 {MAX_RETRIES} 次后仍失败")
                                return result
                        else:
                            return result
        except asyncio.TimeoutError:
            result["error"] = "请求超时"
            retries += 1
            if retries < MAX_RETRIES:
                print(f"行 {index+1} 模型 {config['model']} 请求超时，第 {retries} 次重试...")
                await asyncio.sleep(RETRY_DELAY)
                continue
            else:
                print(f"行 {index+1} 模型 {config['model']} 重试 {MAX_RETRIES} 次后仍超时")
                return result
        except Exception as e:
            result["error"] = f"请求异常: {str(e)}"
            retries += 1
            if retries < MAX_RETRIES:
                print(f"行 {index+1} 模型 {config['model']} 请求异常: {str(e)}，第 {retries} 次重试...")
                await asyncio.sleep(RETRY_DELAY)
                continue
            else:
                print(f"行 {index+1} 模型 {config['model']} 重试 {MAX_RETRIES} 次后仍异常")
                return result
    
    return result

def save_results(df, filename):
    """保存结果到Excel文件"""
    try:
        df.to_excel(filename, index=False)
        print(f"结果已保存到 {filename}")
    except Exception as e:
        print(f"保存结果时出错: {str(e)}")
        # 尝试保存到备用文件
        backup_file = f"backup_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        try:
            df.to_excel(backup_file, index=False)
            print(f"结果已保存到备用文件 {backup_file}")
        except:
            print("无法保存结果，请检查文件权限或磁盘空间")

async def process_batch(prompts, start_idx, batch_size):
    """处理一批数据"""
    tasks = []
    end_idx = min(start_idx + batch_size, len(prompts))
    
    for i in range(start_idx, end_idx):
        formatted_prompt = USER_PROMPT_TEMPLATE.format(prompts[i])
        task = call_api_with_model_switching(formatted_prompt, i)
        tasks.append(task)
    
    return await asyncio.gather(*tasks)

async def main():
    # 检查输入文件是否存在
    if not os.path.exists(INPUT_FILE):
        print(f"错误: 找不到输入文件 {INPUT_FILE}")
        return
    
    try:
        # 读取Excel文件
        print(f"正在读取 {INPUT_FILE}...")
        df = pd.read_excel(INPUT_FILE)
        
        if 'A' not in df.columns and 0 not in df.columns:
            print("错误: Excel文件中没有A列或第一列数据")
            return
        
        # 获取A列数据
        column_name = 'A' if 'A' in df.columns else 0
        prompts = df[column_name].astype(str).tolist()
        total_prompts = len(prompts)
        
        print(f"共读取 {total_prompts} 条数据")
        
        # 添加结果列
        df['response'] = ''
        df['model'] = ''
        df['success'] = False
        df['error'] = ''
        df['prompt_tokens'] = 0
        df['completion_tokens'] = 0
        df['total_tokens'] = 0
        
        # 初始化会话
        await setup_session()
        
        # 处理所有数据
        processed_count = 0
        
        with tqdm(total=total_prompts, desc="处理进度") as pbar:
            # 按批次处理任务
            for i in range(0, total_prompts, SAVE_FREQUENCY):
                batch_results = await process_batch(prompts, i, SAVE_FREQUENCY)
                
                # 更新结果
                for result in batch_results:
                    row_index = result["index"]
                    df.at[row_index, 'response'] = result["content"]
                    df.at[row_index, 'model'] = result["model"]
                    df.at[row_index, 'success'] = result["success"]
                    df.at[row_index, 'error'] = result["error"]
                    df.at[row_index, 'prompt_tokens'] = result["prompt_tokens"]
                    df.at[row_index, 'completion_tokens'] = result["completion_tokens"]
                    df.at[row_index, 'total_tokens'] = result["total_tokens"]
                
                # 更新进度
                processed_count += len(batch_results)
                pbar.update(len(batch_results))
                
                # 每处理SAVE_FREQUENCY条数据保存一次
                save_results(df, OUTPUT_FILE)
        
        # 最后保存一次结果
        save_results(df, OUTPUT_FILE)
        
        # 统计成功和失败的数量
        success_count = df['success'].sum()
        fail_count = total_prompts - success_count
        
        print(f"所有数据处理完成! 成功: {success_count}, 失败: {fail_count}")
        
        # 关闭会话
        await cleanup_session()
        
    except Exception as e:
        print(f"处理过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
        
        # 确保会话被关闭
        try:
            await cleanup_session()
        except:
            pass

if __name__ == "__main__":
    print("开始批量处理数据...")
    
    # 安装依赖
    try:
        import nest_asyncio
    except ImportError:
        print("正在安装必要的依赖...")
        import subprocess
        import sys
        subprocess.check_call([sys.executable, "-m", "pip", "install", "nest_asyncio"])
        import nest_asyncio
    
    # 运行主函数
    asyncio.run(main()) 