[{"success": false, "document_id": "040e8b41-7e49-43ee-b5bc-511aa1dcf44b", "error": "HTTPSConnectionPool(host='api.dify.ai', port=443): Max retries exceeded with url: /v1/datasets/2892e0af-48c9-4b14-ba94-9835232b430a/documents/040e8b41-7e49-43ee-b5bc-511aa1dcf44b/segments (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1129)')))"}, {"success": false, "document_id": "040e8b41-7e49-43ee-b5bc-511aa1dcf44b", "error": "HTTPSConnectionPool(host='api.dify.ai', port=443): Max retries exceeded with url: /v1/datasets/2892e0af-48c9-4b14-ba94-9835232b430a/documents/040e8b41-7e49-43ee-b5bc-511aa1dcf44b/segments (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1129)')))"}, {"success": false, "document_id": "040e8b41-7e49-43ee-b5bc-511aa1dcf44b", "error": "HTTPSConnectionPool(host='api.dify.ai', port=443): Max retries exceeded with url: /v1/datasets/2892e0af-48c9-4b14-ba94-9835232b430a/documents/040e8b41-7e49-43ee-b5bc-511aa1dcf44b/segments (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1129)')))"}, {"success": false, "document_id": "040e8b41-7e49-43ee-b5bc-511aa1dcf44b", "error": "HTTPSConnectionPool(host='api.dify.ai', port=443): Max retries exceeded with url: /v1/datasets/2892e0af-48c9-4b14-ba94-9835232b430a/documents/040e8b41-7e49-43ee-b5bc-511aa1dcf44b/segments (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1129)')))"}, {"success": false, "document_id": "040e8b41-7e49-43ee-b5bc-511aa1dcf44b", "error": "HTTPSConnectionPool(host='api.dify.ai', port=443): Max retries exceeded with url: /v1/datasets/2892e0af-48c9-4b14-ba94-9835232b430a/documents/040e8b41-7e49-43ee-b5bc-511aa1dcf44b/segments (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1129)')))"}, {"success": false, "document_id": "040e8b41-7e49-43ee-b5bc-511aa1dcf44b", "error": "HTTPSConnectionPool(host='api.dify.ai', port=443): Max retries exceeded with url: /v1/datasets/2892e0af-48c9-4b14-ba94-9835232b430a/documents/040e8b41-7e49-43ee-b5bc-511aa1dcf44b/segments (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1129)')))"}, {"success": false, "document_id": "040e8b41-7e49-43ee-b5bc-511aa1dcf44b", "error": "HTTPSConnectionPool(host='api.dify.ai', port=443): Max retries exceeded with url: /v1/datasets/2892e0af-48c9-4b14-ba94-9835232b430a/documents/040e8b41-7e49-43ee-b5bc-511aa1dcf44b/segments (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1129)')))"}, {"success": false, "document_id": "040e8b41-7e49-43ee-b5bc-511aa1dcf44b", "error": "HTTPSConnectionPool(host='api.dify.ai', port=443): Max retries exceeded with url: /v1/datasets/2892e0af-48c9-4b14-ba94-9835232b430a/documents/040e8b41-7e49-43ee-b5bc-511aa1dcf44b/segments (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1129)')))"}, {"success": false, "document_id": "040e8b41-7e49-43ee-b5bc-511aa1dcf44b", "error": "HTTPSConnectionPool(host='api.dify.ai', port=443): Max retries exceeded with url: /v1/datasets/2892e0af-48c9-4b14-ba94-9835232b430a/documents/040e8b41-7e49-43ee-b5bc-511aa1dcf44b/segments (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1129)')))"}, {"success": false, "document_id": "040e8b41-7e49-43ee-b5bc-511aa1dcf44b", "error": "HTTPSConnectionPool(host='api.dify.ai', port=443): Max retries exceeded with url: /v1/datasets/2892e0af-48c9-4b14-ba94-9835232b430a/documents/040e8b41-7e49-43ee-b5bc-511aa1dcf44b/segments (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1129)')))"}, {"success": false, "document_id": "040e8b41-7e49-43ee-b5bc-511aa1dcf44b", "error": "HTTPSConnectionPool(host='api.dify.ai', port=443): Max retries exceeded with url: /v1/datasets/2892e0af-48c9-4b14-ba94-9835232b430a/documents/040e8b41-7e49-43ee-b5bc-511aa1dcf44b/segments (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1129)')))"}, {"success": false, "document_id": "040e8b41-7e49-43ee-b5bc-511aa1dcf44b", "error": "HTTPSConnectionPool(host='api.dify.ai', port=443): Max retries exceeded with url: /v1/datasets/2892e0af-48c9-4b14-ba94-9835232b430a/documents/040e8b41-7e49-43ee-b5bc-511aa1dcf44b/segments (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1129)')))"}, {"success": false, "document_id": "040e8b41-7e49-43ee-b5bc-511aa1dcf44b", "error": "HTTPSConnectionPool(host='api.dify.ai', port=443): Max retries exceeded with url: /v1/datasets/2892e0af-48c9-4b14-ba94-9835232b430a/documents/040e8b41-7e49-43ee-b5bc-511aa1dcf44b/segments (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1129)')))"}, {"success": false, "document_id": "040e8b41-7e49-43ee-b5bc-511aa1dcf44b", "error": "HTTPSConnectionPool(host='api.dify.ai', port=443): Max retries exceeded with url: /v1/datasets/2892e0af-48c9-4b14-ba94-9835232b430a/documents/040e8b41-7e49-43ee-b5bc-511aa1dcf44b/segments (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1129)')))"}, {"success": false, "document_id": "040e8b41-7e49-43ee-b5bc-511aa1dcf44b", "error": "HTTPSConnectionPool(host='api.dify.ai', port=443): Max retries exceeded with url: /v1/datasets/2892e0af-48c9-4b14-ba94-9835232b430a/documents/040e8b41-7e49-43ee-b5bc-511aa1dcf44b/segments (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1129)')))"}, {"success": false, "document_id": "040e8b41-7e49-43ee-b5bc-511aa1dcf44b", "error": "HTTPSConnectionPool(host='api.dify.ai', port=443): Max retries exceeded with url: /v1/datasets/2892e0af-48c9-4b14-ba94-9835232b430a/documents/040e8b41-7e49-43ee-b5bc-511aa1dcf44b/segments (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1129)')))"}, {"success": false, "document_id": "040e8b41-7e49-43ee-b5bc-511aa1dcf44b", "error": "HTTPSConnectionPool(host='api.dify.ai', port=443): Max retries exceeded with url: /v1/datasets/2892e0af-48c9-4b14-ba94-9835232b430a/documents/040e8b41-7e49-43ee-b5bc-511aa1dcf44b/segments (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1129)')))"}, {"success": false, "document_id": "040e8b41-7e49-43ee-b5bc-511aa1dcf44b", "error": "HTTPSConnectionPool(host='api.dify.ai', port=443): Max retries exceeded with url: /v1/datasets/2892e0af-48c9-4b14-ba94-9835232b430a/documents/040e8b41-7e49-43ee-b5bc-511aa1dcf44b/segments (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1129)')))"}, {"success": false, "document_id": "040e8b41-7e49-43ee-b5bc-511aa1dcf44b", "error": "HTTPSConnectionPool(host='api.dify.ai', port=443): Max retries exceeded with url: /v1/datasets/2892e0af-48c9-4b14-ba94-9835232b430a/documents/040e8b41-7e49-43ee-b5bc-511aa1dcf44b/segments (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1129)')))"}, {"success": false, "document_id": "040e8b41-7e49-43ee-b5bc-511aa1dcf44b", "error": "HTTPSConnectionPool(host='api.dify.ai', port=443): Max retries exceeded with url: /v1/datasets/2892e0af-48c9-4b14-ba94-9835232b430a/documents/040e8b41-7e49-43ee-b5bc-511aa1dcf44b/segments (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1129)')))"}, {"success": false, "document_id": "040e8b41-7e49-43ee-b5bc-511aa1dcf44b", "error": "HTTPSConnectionPool(host='api.dify.ai', port=443): Max retries exceeded with url: /v1/datasets/2892e0af-48c9-4b14-ba94-9835232b430a/documents/040e8b41-7e49-43ee-b5bc-511aa1dcf44b/segments (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1129)')))"}, {"success": false, "document_id": "040e8b41-7e49-43ee-b5bc-511aa1dcf44b", "error": "HTTPSConnectionPool(host='api.dify.ai', port=443): Max retries exceeded with url: /v1/datasets/2892e0af-48c9-4b14-ba94-9835232b430a/documents/040e8b41-7e49-43ee-b5bc-511aa1dcf44b/segments (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1129)')))"}, {"success": false, "document_id": "040e8b41-7e49-43ee-b5bc-511aa1dcf44b", "error": "HTTPSConnectionPool(host='api.dify.ai', port=443): Max retries exceeded with url: /v1/datasets/2892e0af-48c9-4b14-ba94-9835232b430a/documents/040e8b41-7e49-43ee-b5bc-511aa1dcf44b/segments (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1129)')))"}, {"success": false, "document_id": "040e8b41-7e49-43ee-b5bc-511aa1dcf44b", "error": "HTTPSConnectionPool(host='api.dify.ai', port=443): Max retries exceeded with url: /v1/datasets/2892e0af-48c9-4b14-ba94-9835232b430a/documents/040e8b41-7e49-43ee-b5bc-511aa1dcf44b/segments (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1129)')))"}, {"success": false, "document_id": "040e8b41-7e49-43ee-b5bc-511aa1dcf44b", "error": "HTTPSConnectionPool(host='api.dify.ai', port=443): Max retries exceeded with url: /v1/datasets/2892e0af-48c9-4b14-ba94-9835232b430a/documents/040e8b41-7e49-43ee-b5bc-511aa1dcf44b/segments (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1129)')))"}, {"success": false, "document_id": "040e8b41-7e49-43ee-b5bc-511aa1dcf44b", "error": "HTTPSConnectionPool(host='api.dify.ai', port=443): Max retries exceeded with url: /v1/datasets/2892e0af-48c9-4b14-ba94-9835232b430a/documents/040e8b41-7e49-43ee-b5bc-511aa1dcf44b/segments (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1129)')))"}, {"success": false, "document_id": "040e8b41-7e49-43ee-b5bc-511aa1dcf44b", "error": "HTTPSConnectionPool(host='api.dify.ai', port=443): Max retries exceeded with url: /v1/datasets/2892e0af-48c9-4b14-ba94-9835232b430a/documents/040e8b41-7e49-43ee-b5bc-511aa1dcf44b/segments (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1129)')))"}, {"success": false, "document_id": "040e8b41-7e49-43ee-b5bc-511aa1dcf44b", "error": "HTTPSConnectionPool(host='api.dify.ai', port=443): Max retries exceeded with url: /v1/datasets/2892e0af-48c9-4b14-ba94-9835232b430a/documents/040e8b41-7e49-43ee-b5bc-511aa1dcf44b/segments (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1129)')))"}]