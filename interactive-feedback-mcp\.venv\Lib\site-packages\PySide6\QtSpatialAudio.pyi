# Copyright (C) 2022 The Qt Company Ltd.
# SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
from __future__ import annotations
"""
This file contains the exact signatures for all functions in module
PySide6.QtSpatialAudio, except for defaults which are replaced by "...".

# mypy: disable-error-code="override, overload-overlap"
"""

# Module `PySide6.QtSpatialAudio`

import PySide6.QtSpatialAudio
import PySide6.QtCore
import PySide6.QtGui
import PySide6.QtMultimedia

import enum
import typing
from PySide6.QtCore import Signal


class QAmbientSound(PySide6.QtCore.QObject):

    autoPlayChanged          : typing.ClassVar[Signal] = ... # autoPlayChanged()
    loopsChanged             : typing.ClassVar[Signal] = ... # loopsChanged()
    sourceChanged            : typing.ClassVar[Signal] = ... # sourceChanged()
    volumeChanged            : typing.ClassVar[Signal] = ... # volumeChanged()

    class Loops(enum.IntEnum):

        Infinite                  = ...  # -1
        Once                      = ...  # 0x1


    def __init__(self, engine: PySide6.QtSpatialAudio.QAudioEngine, /, *, source: PySide6.QtCore.QUrl | None= ..., volume: float | None= ..., loops: int | None= ..., autoPlay: bool | None= ...) -> None: ...

    def autoPlay(self, /) -> bool: ...
    def engine(self, /) -> PySide6.QtSpatialAudio.QAudioEngine: ...
    def loops(self, /) -> int: ...
    def pause(self, /) -> None: ...
    def play(self, /) -> None: ...
    def setAutoPlay(self, autoPlay: bool, /) -> None: ...
    def setLoops(self, loops: int, /) -> None: ...
    def setSource(self, url: PySide6.QtCore.QUrl | str, /) -> None: ...
    def setVolume(self, volume: float, /) -> None: ...
    def source(self, /) -> PySide6.QtCore.QUrl: ...
    def stop(self, /) -> None: ...
    def volume(self, /) -> float: ...


class QAudioEngine(PySide6.QtCore.QObject):

    distanceScaleChanged     : typing.ClassVar[Signal] = ... # distanceScaleChanged()
    masterVolumeChanged      : typing.ClassVar[Signal] = ... # masterVolumeChanged()
    outputDeviceChanged      : typing.ClassVar[Signal] = ... # outputDeviceChanged()
    outputModeChanged        : typing.ClassVar[Signal] = ... # outputModeChanged()
    pausedChanged            : typing.ClassVar[Signal] = ... # pausedChanged()

    class OutputMode(enum.Enum):

        Surround                  = ...  # 0x0
        Stereo                    = ...  # 0x1
        Headphone                 = ...  # 0x2


    @typing.overload
    def __init__(self, parent: PySide6.QtCore.QObject, /, *, outputMode: PySide6.QtSpatialAudio.QAudioEngine.OutputMode | None= ..., outputDevice: PySide6.QtMultimedia.QAudioDevice | None= ..., masterVolume: float | None= ..., paused: bool | None= ..., distanceScale: float | None= ...) -> None: ...
    @typing.overload
    def __init__(self, /, *, outputMode: PySide6.QtSpatialAudio.QAudioEngine.OutputMode | None= ..., outputDevice: PySide6.QtMultimedia.QAudioDevice | None= ..., masterVolume: float | None= ..., paused: bool | None= ..., distanceScale: float | None= ...) -> None: ...
    @typing.overload
    def __init__(self, sampleRate: int, /, parent: PySide6.QtCore.QObject | None= ..., *, outputMode: PySide6.QtSpatialAudio.QAudioEngine.OutputMode | None= ..., outputDevice: PySide6.QtMultimedia.QAudioDevice | None= ..., masterVolume: float | None= ..., paused: bool | None= ..., distanceScale: float | None= ...) -> None: ...

    def distanceScale(self, /) -> float: ...
    def masterVolume(self, /) -> float: ...
    def outputDevice(self, /) -> PySide6.QtMultimedia.QAudioDevice: ...
    def outputMode(self, /) -> PySide6.QtSpatialAudio.QAudioEngine.OutputMode: ...
    def pause(self, /) -> None: ...
    def paused(self, /) -> bool: ...
    def resume(self, /) -> None: ...
    def roomEffectsEnabled(self, /) -> bool: ...
    def sampleRate(self, /) -> int: ...
    def setDistanceScale(self, scale: float, /) -> None: ...
    def setMasterVolume(self, volume: float, /) -> None: ...
    def setOutputDevice(self, device: PySide6.QtMultimedia.QAudioDevice, /) -> None: ...
    def setOutputMode(self, mode: PySide6.QtSpatialAudio.QAudioEngine.OutputMode, /) -> None: ...
    def setPaused(self, paused: bool, /) -> None: ...
    def setRoomEffectsEnabled(self, enabled: bool, /) -> None: ...
    def start(self, /) -> None: ...
    def stop(self, /) -> None: ...


class QAudioListener(PySide6.QtCore.QObject):

    def __init__(self, engine: PySide6.QtSpatialAudio.QAudioEngine, /) -> None: ...

    def engine(self, /) -> PySide6.QtSpatialAudio.QAudioEngine: ...
    def position(self, /) -> PySide6.QtGui.QVector3D: ...
    def rotation(self, /) -> PySide6.QtGui.QQuaternion: ...
    def setPosition(self, pos: PySide6.QtGui.QVector3D, /) -> None: ...
    def setRotation(self, q: PySide6.QtGui.QQuaternion, /) -> None: ...


class QAudioRoom(PySide6.QtCore.QObject):

    dimensionsChanged        : typing.ClassVar[Signal] = ... # dimensionsChanged()
    positionChanged          : typing.ClassVar[Signal] = ... # positionChanged()
    reflectionGainChanged    : typing.ClassVar[Signal] = ... # reflectionGainChanged()
    reverbBrightnessChanged  : typing.ClassVar[Signal] = ... # reverbBrightnessChanged()
    reverbGainChanged        : typing.ClassVar[Signal] = ... # reverbGainChanged()
    reverbTimeChanged        : typing.ClassVar[Signal] = ... # reverbTimeChanged()
    rotationChanged          : typing.ClassVar[Signal] = ... # rotationChanged()
    wallsChanged             : typing.ClassVar[Signal] = ... # wallsChanged()

    class Material(enum.Enum):

        Transparent               = ...  # 0x0
        AcousticCeilingTiles      = ...  # 0x1
        BrickBare                 = ...  # 0x2
        BrickPainted              = ...  # 0x3
        ConcreteBlockCoarse       = ...  # 0x4
        ConcreteBlockPainted      = ...  # 0x5
        CurtainHeavy              = ...  # 0x6
        FiberGlassInsulation      = ...  # 0x7
        GlassThin                 = ...  # 0x8
        GlassThick                = ...  # 0x9
        Grass                     = ...  # 0xa
        LinoleumOnConcrete        = ...  # 0xb
        Marble                    = ...  # 0xc
        Metal                     = ...  # 0xd
        ParquetOnConcrete         = ...  # 0xe
        PlasterRough              = ...  # 0xf
        PlasterSmooth             = ...  # 0x10
        PlywoodPanel              = ...  # 0x11
        PolishedConcreteOrTile    = ...  # 0x12
        Sheetrock                 = ...  # 0x13
        WaterOrIceSurface         = ...  # 0x14
        WoodCeiling               = ...  # 0x15
        WoodPanel                 = ...  # 0x16
        UniformMaterial           = ...  # 0x17

    class Wall(enum.Enum):

        LeftWall                  = ...  # 0x0
        RightWall                 = ...  # 0x1
        Floor                     = ...  # 0x2
        Ceiling                   = ...  # 0x3
        FrontWall                 = ...  # 0x4
        BackWall                  = ...  # 0x5


    def __init__(self, engine: PySide6.QtSpatialAudio.QAudioEngine, /, *, position: PySide6.QtGui.QVector3D | None= ..., dimensions: PySide6.QtGui.QVector3D | None= ..., rotation: PySide6.QtGui.QQuaternion | None= ..., reflectionGain: float | None= ..., reverbGain: float | None= ..., reverbTime: float | None= ..., reverbBrightness: float | None= ...) -> None: ...

    def dimensions(self, /) -> PySide6.QtGui.QVector3D: ...
    def position(self, /) -> PySide6.QtGui.QVector3D: ...
    def reflectionGain(self, /) -> float: ...
    def reverbBrightness(self, /) -> float: ...
    def reverbGain(self, /) -> float: ...
    def reverbTime(self, /) -> float: ...
    def rotation(self, /) -> PySide6.QtGui.QQuaternion: ...
    def setDimensions(self, dim: PySide6.QtGui.QVector3D, /) -> None: ...
    def setPosition(self, pos: PySide6.QtGui.QVector3D, /) -> None: ...
    def setReflectionGain(self, factor: float, /) -> None: ...
    def setReverbBrightness(self, factor: float, /) -> None: ...
    def setReverbGain(self, factor: float, /) -> None: ...
    def setReverbTime(self, factor: float, /) -> None: ...
    def setRotation(self, q: PySide6.QtGui.QQuaternion, /) -> None: ...
    def setWallMaterial(self, wall: PySide6.QtSpatialAudio.QAudioRoom.Wall, material: PySide6.QtSpatialAudio.QAudioRoom.Material, /) -> None: ...
    def wallMaterial(self, wall: PySide6.QtSpatialAudio.QAudioRoom.Wall, /) -> PySide6.QtSpatialAudio.QAudioRoom.Material: ...


class QIntList(object): ...


class QSpatialSound(PySide6.QtCore.QObject):

    autoPlayChanged          : typing.ClassVar[Signal] = ... # autoPlayChanged()
    directivityChanged       : typing.ClassVar[Signal] = ... # directivityChanged()
    directivityOrderChanged  : typing.ClassVar[Signal] = ... # directivityOrderChanged()
    distanceCutoffChanged    : typing.ClassVar[Signal] = ... # distanceCutoffChanged()
    distanceModelChanged     : typing.ClassVar[Signal] = ... # distanceModelChanged()
    loopsChanged             : typing.ClassVar[Signal] = ... # loopsChanged()
    manualAttenuationChanged : typing.ClassVar[Signal] = ... # manualAttenuationChanged()
    nearFieldGainChanged     : typing.ClassVar[Signal] = ... # nearFieldGainChanged()
    occlusionIntensityChanged: typing.ClassVar[Signal] = ... # occlusionIntensityChanged()
    positionChanged          : typing.ClassVar[Signal] = ... # positionChanged()
    rotationChanged          : typing.ClassVar[Signal] = ... # rotationChanged()
    sizeChanged              : typing.ClassVar[Signal] = ... # sizeChanged()
    sourceChanged            : typing.ClassVar[Signal] = ... # sourceChanged()
    volumeChanged            : typing.ClassVar[Signal] = ... # volumeChanged()

    class DistanceModel(enum.Enum):

        Logarithmic               = ...  # 0x0
        Linear                    = ...  # 0x1
        ManualAttenuation         = ...  # 0x2

    class Loops(enum.IntEnum):

        Infinite                  = ...  # -1
        Once                      = ...  # 0x1


    def __init__(self, engine: PySide6.QtSpatialAudio.QAudioEngine, /, *, source: PySide6.QtCore.QUrl | None= ..., position: PySide6.QtGui.QVector3D | None= ..., rotation: PySide6.QtGui.QQuaternion | None= ..., volume: float | None= ..., distanceModel: PySide6.QtSpatialAudio.QSpatialSound.DistanceModel | None= ..., size: float | None= ..., distanceCutoff: float | None= ..., manualAttenuation: float | None= ..., occlusionIntensity: float | None= ..., directivity: float | None= ..., directivityOrder: float | None= ..., nearFieldGain: float | None= ..., loops: int | None= ..., autoPlay: bool | None= ...) -> None: ...

    def autoPlay(self, /) -> bool: ...
    def directivity(self, /) -> float: ...
    def directivityOrder(self, /) -> float: ...
    def distanceCutoff(self, /) -> float: ...
    def distanceModel(self, /) -> PySide6.QtSpatialAudio.QSpatialSound.DistanceModel: ...
    def engine(self, /) -> PySide6.QtSpatialAudio.QAudioEngine: ...
    def loops(self, /) -> int: ...
    def manualAttenuation(self, /) -> float: ...
    def nearFieldGain(self, /) -> float: ...
    def occlusionIntensity(self, /) -> float: ...
    def pause(self, /) -> None: ...
    def play(self, /) -> None: ...
    def position(self, /) -> PySide6.QtGui.QVector3D: ...
    def rotation(self, /) -> PySide6.QtGui.QQuaternion: ...
    def setAutoPlay(self, autoPlay: bool, /) -> None: ...
    def setDirectivity(self, alpha: float, /) -> None: ...
    def setDirectivityOrder(self, alpha: float, /) -> None: ...
    def setDistanceCutoff(self, cutoff: float, /) -> None: ...
    def setDistanceModel(self, model: PySide6.QtSpatialAudio.QSpatialSound.DistanceModel, /) -> None: ...
    def setLoops(self, loops: int, /) -> None: ...
    def setManualAttenuation(self, attenuation: float, /) -> None: ...
    def setNearFieldGain(self, gain: float, /) -> None: ...
    def setOcclusionIntensity(self, occlusion: float, /) -> None: ...
    def setPosition(self, pos: PySide6.QtGui.QVector3D, /) -> None: ...
    def setRotation(self, q: PySide6.QtGui.QQuaternion, /) -> None: ...
    def setSize(self, size: float, /) -> None: ...
    def setSource(self, url: PySide6.QtCore.QUrl | str, /) -> None: ...
    def setVolume(self, volume: float, /) -> None: ...
    def size(self, /) -> float: ...
    def source(self, /) -> PySide6.QtCore.QUrl: ...
    def stop(self, /) -> None: ...
    def volume(self, /) -> float: ...


# eof
