import requests
import json

# API配置
base_url = "https://v1.voct.top/v1"
api_key = "sk-cTifeFuCoaFG3PSRpy684RS8NkpP5NNIltLkyajjG2Q"

# 请求头
headers = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {api_key}"
}

# 获取所有可用模型
def list_models():
    url = f"{base_url}/models"
    try:
        response = requests.get(url, headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if 'data' in data and isinstance(data['data'], list):
                print("\n可用模型列表:")
                print("-" * 50)
                for i, model in enumerate(data['data'], 1):
                    print(f"{i}. {model['id']} (提供者: {model['owned_by']})")
                print("-" * 50)
                return data
            else:
                return f"响应格式不符合预期: {data}"
        else:
            return f"请求失败，状态码: {response.status_code}, 响应: {response.text}"
    except Exception as e:
        return f"发生错误: {str(e)}"

if __name__ == "__main__":
    print("正在获取所有可用模型...")
    result = list_models()
    print("\n结果:")
    print(result) 