import json
import re

def categorize_content(content_text):
    content_lower = content_text.lower() # 统一转小写处理，便于匹配

    # 规则 1: 售前(出售权限)
    keywords_presales_permissions = ["成為賣家", "銷售許可", "銷售資格", "保字", "開店流程", "賣家身份", "業務範圍"]
    if any(kw in content_text for kw in keywords_presales_permissions) or \
       ("如何開始在平台銷售" in content_text or "獲取銷售特定商品的資格" in content_text):
        return "售前(出售权限)"

    # 规则 3: 售后(纠纷争议) - 优先判断，因为关键词较明确且重要
    keywords_aftersales_disputes = [
        "商品與描述不符", "商品和描述不符", "服務質量未達承諾", "帳號被找回", "帳號找回", "退款", "退貨", 
        "換貨", "欺詐", "詐騙", "平台介入", "惡意評價", "不履行協議", "坐地起價", "投訴", "申訴"
    ] 
    if any(kw in content_text for kw in keywords_aftersales_disputes) or \
       ( ("交易結果不符合預期" in content_text or "權益受損" in content_text or "不滿" in content_text) and \
         ("平台介入" in content_text or "裁決" in content_text or "補救" in content_text or "客服協助處理" in content_text or "客服處理" in content_text or "客服幫忙" in content_text) ):
        if ("取消訂單" in content_text and "退款" in content_text and ("客服" in content_text or "平台" in content_text)) or \
           ("賣家原因取消" in content_text or "買家原因取消" in content_text) or \
           ("帳號被盜用" in content_text and ("找回" in content_text or "被盜" in content_text)):
             return "售后(纠纷争议)"

    # 规则 2: 售中(交易问题)
    keywords_insales_progress = ["訂單處理狀態", "物流進度", "交付進程", "修改未完成訂單", "聯繫交易對方", "延長交易時間", "交易進行中", "順利推進", "推進交易"]
    if any(kw in content_text for kw in keywords_insales_progress) and not any(kw in content_text for kw in keywords_aftersales_disputes):
        return "售中(交易问题)"

    # 规则 4: 註冊登入
    keywords_reg_login = [
        "創建帳號", "建立帳號", "註冊帳號", "登入", "無法註冊", "收不到驗證碼", "收不到郵件", "忘記帳號", "忘記密碼", 
        "無法登入", "密碼錯誤", "帳號被鎖定", "登錄帳號的密碼"
    ]
    if any(kw in content_text for kw in keywords_reg_login):
        return "註冊登入"

    # 规则 5: 身份/安全驗證
    keywords_id_security_verification = [
        "實名認證", "人臉識別", "證件上傳", "健保卡驗證", "綁定手機", "修改手機", "安全手機", 
        "支付密碼", "二步驗證", "風控", "安全驗證", "驗證失敗", "相似度過低", "出生日期錯誤"
    ]
    if any(kw in content_text for kw in keywords_id_security_verification):
        return "身份/安全驗證"

    # 规则 6: 發佈商品
    keywords_product_listing = [
        "創建商品", "編輯商品", "上架", "下架", "管理商品", "刊登規則", "重複刊登", 
        "刊登區域不符", "商品違規", "刪除商品", "警告商品", "審核狀態"
    ]
    if any(kw in content_text for kw in keywords_product_listing):
        return "發佈商品"

    # 规则 7: 儲值點數
    keywords_points_topup = [
        "充值", "儲值", "充入資金", "購買點數", "平台點數", "代幣", "款項未到帳", "儲值方式", 
        "儲值渠道", "儲值金額", "儲值記錄", "儲值失敗"
    ]
    if any(kw in content_text for kw in keywords_points_topup):
        if not any(k in content_text for k in ["訂單付款", "購買商品", "繳費"]):
            return "儲值點數"

    # 规则 8: 提取賬戶款項
    keywords_fund_withdrawal = [
        "餘額提取", "提現", "提款", "提領", "提現失敗", "提現處理", "提現時長", "提款手續費", 
        "收款帳戶", "銀行帳戶"
    ]
    if any(kw in content_text for kw in keywords_fund_withdrawal):
        return "提取賬戶款項"
        
    # 规则 9: 付款購買商品
    keywords_payment_for_goods = [
        "支付操作", "選擇支付方式", "支付渠道失敗", "繳費異常", "無法開啟交易", "優惠券", "折扣碼", 
        "支付頁面錯誤", "購買權限", "無法付款", "訂單金額", "支付流程", "繳費", "付款"
    ]
    if any(kw in content_text for kw in keywords_payment_for_goods) or \
       ("訂單" in content_text and ("付款" in content_text or "繳費" in content_text or "支付" in content_text)):
        return "付款購買商品"

    # 规则 10: 交易安全
    keywords_transaction_security = [
        "帳號被盜", "帳號盜用", "未授權操作", "密碼被更改", "綁定被移除", "釣魚", "詐騙信息", 
        "可疑連結", "線下交易", "私下交易", "舉報虛假", "舉報不實", "贓號", "可信度懷疑", "賣家找回帳號"
    ]
    if any(kw in content_text for kw in keywords_transaction_security) or \
       (("安全" in content_text or "風險" in content_text) and ("交易" in content_text or "帳戶" in content_text or "信息洩露" in content_text)):
        if not any(kw in content_text for kw in keywords_aftersales_disputes):
             return "交易安全"
        if not ("退款" in content_text or "補償" in content_text or "賠償" in content_text):
            return "交易安全"

    # 规则 11: 平台穩定性
    keywords_platform_stability = [
        "頁面無法打開", "系統崩潰", "系統卡頓", "功能無響應", "功能報錯", "數據加載過慢", 
        "服務器錯誤", "APP閃退", "無法使用", "打不開", "閃退", "卡頓"
    ]
    if any(kw in content_text for kw in keywords_platform_stability):
        return "平台穩定性"

    # 规则 12: 交易環境
    keywords_trading_environment = [
        "交易秩序", "市場公平", "平台規則", "平台政策", "手續費", "保證金", "違規處罰", 
        "刷單", "虛假瀏覽", "數據操縱", "重複刊登", "惡意低價", "不正當競爭", 
        "處罰申訴", "禁言", "停權", "帳戶權限", "問與答被鎖", "官方公告", "政策變更", 
        "用戶間口角", "騷擾", "瀏覽量"
    ]
    if any(kw in content_text for kw in keywords_trading_environment):
        return "交易環境"

    # 规则 13: 操作交互及UI設計
    keywords_ui_ux = [
        "界面", "視覺", "按鈕位置", "找不到", "操作流程", "便捷性", "易用性", 
        "會員中心", "刷新", "提示信息", "用戶體驗", "好用", "美觀", "方便", "建議修改", "UI", "UX"
    ]
    if any(kw in content_text for kw in keywords_ui_ux):
        return "操作交互及UI設計"

    # 规则 14: 客戶服務
    keywords_customer_service = [
        "客服人員", "人工客服", "AI客服", "客服溝通", "客服互動", "客服響應", "客服態度", 
        "客服辛苦了", "客服解決能力", "客服專業", "電話等待", "聊天系統", "客服回复", "聯繫客服"
    ]
    is_about_specific_issue = False
    all_other_strong_keywords = keywords_presales_permissions + keywords_aftersales_disputes + keywords_insales_progress + \
                               keywords_reg_login + keywords_id_security_verification + keywords_product_listing + \
                               keywords_points_topup + keywords_fund_withdrawal + keywords_payment_for_goods + \
                               keywords_transaction_security + keywords_platform_stability + keywords_trading_environment + keywords_ui_ux
    if any(kw in content_text for kw in all_other_strong_keywords):
        is_about_specific_issue = True

    if not is_about_specific_issue and any(kw in content_text for kw in keywords_customer_service):
        if ("客服態度" in content_text or "客服效率" in content_text or "感謝客服" in content_text or "客服辛苦" in content_text or "評價客服" in content_text) and not any(kw in content_text for kw in ["退款", "訂單", "帳號", "安全", "儲值", "提現"]):
             return "客戶服務"
        if "聯繫客服" in content_text and sum(1 for kw_other in all_other_strong_keywords if kw_other in content_text) > 1 :
            pass 
        elif any(kw in content_text for kw in keywords_customer_service):
             non_cs_keywords_count = 0
             for kw_set in [keywords_presales_permissions, keywords_aftersales_disputes, keywords_insales_progress, keywords_reg_login, keywords_id_security_verification, keywords_product_listing, keywords_points_topup, keywords_fund_withdrawal, keywords_payment_for_goods, keywords_transaction_security, keywords_platform_stability, keywords_trading_environment, keywords_ui_ux]:
                 if any(kw in content_text for kw in kw_set):
                     non_cs_keywords_count +=1
             if non_cs_keywords_count <=1 and any(kw in content_text for kw in ["客服回覆", "客服告知", "客服說", "問客服"]):
                 return "客戶服務"

    # 规则 3 (Re-check)
    if "退款" in content_text or "退貨" in content_text or "換貨" in content_text:
        if "問題" in content_text or "無法" in content_text or "失敗" in content_text or "要求" in content_text or "申請" in content_text or "爭議" in content_text or "糾紛" in content_text:
            return "售后(纠纷争议)"
        if "客服" in content_text and ("協助" in content_text or "幫忙" in content_text or "處理" in content_text):
             return "售后(纠纷争议)"

    # 默认/规则 15: 其他綜合問題
    if "你好" == content_lower.strip() or "您好" == content_text.strip() or len(content_text) < 10 :
        return "其他綜合問題"
        
    return "其他綜合問題"

def main():
    input_file = '1.json'
    output_file = '1.json' 

    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            records = json.load(f)
    except FileNotFoundError:
        print(f"Error: File {input_file} not found.")
        return
    except json.JSONDecodeError:
        print(f"Error: Could not decode JSON from {input_file}.")
        return

    if not isinstance(records, list):
        print(f"Error: Expected a list of records in {input_file}, but got {type(records)}.")
        return
        
    updated_records = 0
    for record in records:
        if 'content' in record and isinstance(record['content'], str):
            original_category = record.get('category', '')
            determined_category = categorize_content(record['content'])
            record['category'] = determined_category
            if original_category != determined_category:
                updated_records +=1
                # print(f"Content: {record['content'][:50]}... \nOriginal: '{original_category}', New: '{determined_category}'\n---") # Commented out for cleaner output
        else:
            # print(f"Warning: Record missing 'content' field or content is not a string: {record}") # Commented out
            if 'category' not in record or not record['category']:
                 record['category'] = "其他綜合問題"

    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(records, f, ensure_ascii=False, indent=2)
        print(f"Successfully processed {len(records)} records. {updated_records} categories updated.")
        print(f"Output saved to {output_file}")
    except IOError:
        print(f"Error: Could not write to output file {output_file}.")

if __name__ == '__main__':
    main() 