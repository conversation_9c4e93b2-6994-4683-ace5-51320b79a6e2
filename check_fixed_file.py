import pandas as pd

# 读取原始文件和修正后的文件
try:
    df_original = pd.read_excel('处理后文件.xlsx')
    df_fixed = pd.read_excel('处理后文件_修正版.xlsx')
    
    print(f"原始文件行数: {len(df_original)}, 修正后文件行数: {len(df_fixed)}")
    
    # 比较修正前后的差异
    # 检查分类修正
    category_changes = []
    for idx, row in df_fixed.iterrows():
        if df_original.loc[idx, '二級分類'] != row['二級分類']:
            category_changes.append({
                '行索引': idx,
                '申訴ID': row['申訴ID'],
                '原分类': df_original.loc[idx, '二級分類'],
                '修正后分类': row['二級分類']
            })
    
    print(f"\n分类修正数量: {len(category_changes)}")
    if category_changes:
        print("\n分类修正详情:")
        for change in category_changes:
            print(f"行索引: {change['行索引']}, 申訴ID: {change['申訴ID']}, 原分类: {change['原分类']} -> 修正后分类: {change['修正后分类']}")
    
    # 检查原话修正
    text_changes = []
    for idx, row in df_fixed.iterrows():
        if pd.isna(df_original.loc[idx, '原話']) != pd.isna(row['原話']):
            text_changes.append({
                '行索引': idx,
                '申訴ID': row['申訴ID'],
                '原原话': df_original.loc[idx, '原話'] if not pd.isna(df_original.loc[idx, '原話']) else "空",
                '修正后原话': row['原話'] if not pd.isna(row['原話']) else "空"
            })
    
    print(f"\n原话修正数量: {len(text_changes)}")
    if text_changes:
        print("\n原话修正详情:")
        for change in text_changes:
            print(f"行索引: {change['行索引']}, 申訴ID: {change['申訴ID']}")
            print(f"原原话: {change['原原话']}")
            print(f"修正后原话: {change['修正后原话']}")
            print("-" * 80)
    
    # 显示仍然为空的原话和建议
    empty_text = df_fixed[df_fixed['原話'].isna()].index.tolist()
    empty_suggestions = df_fixed[df_fixed['建議'].isna()].index.tolist()
    
    print(f"\n仍然有{len(empty_text)}行原话为空")
    print(f"仍然有{len(empty_suggestions)}行建议为空")
    
    # 显示修正后的几行数据
    print("\n修正后的前5行数据:")
    print(df_fixed.head(5)[['申訴ID', '二級分類', '原話']].to_string())
    
except Exception as e:
    print(f"处理文件时出错: {e}") 