import requests
import json

# API配置
base_url = "https://ai.huan666.de/v1"
api_key = "sk-iAdBdJpg6lZmfKokaF8eNuI0PLESIsaxnUtS5nGfInT6cgwk"

# 请求头
headers = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {api_key}"
}

def chat_completion(model_id, messages, temperature=0.7, max_tokens=1000):
    """
    向API发送聊天请求
    
    参数:
        model_id (str): 模型ID
        messages (list): 消息列表，格式为[{"role": "user", "content": "你好"}]
        temperature (float): 温度参数，控制随机性
        max_tokens (int): 最大生成token数
    
    返回:
        dict: API响应
    """
    url = f"{base_url}/chat/completions"
    
    payload = {
        "model": model_id,
        "messages": messages,
        "temperature": temperature,
        "max_tokens": max_tokens
    }
    
    try:
        print(f"正在向模型 {model_id} 发送请求...")
        print(f"请求内容: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        
        response = requests.post(url, headers=headers, json=payload)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            # 提取回复内容
            if 'choices' in result and len(result['choices']) > 0:
                message = result['choices'][0].get('message', {})
                content = message.get('content', '')
                
                print("\n回复内容:")
                print("-" * 50)
                print(content)
                print("-" * 50)
                
                # 显示使用情况
                if 'usage' in result:
                    usage = result['usage']
                    print(f"\n使用情况:")
                    print(f"提示tokens: {usage.get('prompt_tokens', 'N/A')}")
                    print(f"完成tokens: {usage.get('completion_tokens', 'N/A')}")
                    print(f"总tokens: {usage.get('total_tokens', 'N/A')}")
                
                return result
            else:
                print(f"未找到回复内容: {result}")
                return result
        else:
            print(f"请求失败: {response.text}")
            return {"error": response.text}
    except Exception as e:
        print(f"发生错误: {str(e)}")
        return {"error": str(e)}

if __name__ == "__main__":
    # 可用模型列表
    print("可用模型列表:")
    models = [
        "deepseek-r1-250120",
        "deepseek-v3-250324",
        "doubao-1-5-lite-32k-250115",
        "doubao-1.5-thinking-pro",
        "gemini-2.0-flash",
        "gemini-2.5-flash-preview-05-20",
        "gemini-2.5-pro-preview-03-25",
        "gemini-2.5-pro-preview-05-06",
        "grok-3",
        "grok-3-fast",
        "grok-3-fast-beta",
        "grok-3-mini",
        "grok-3-mini-fast",
        "grok-3-mini-fast-beta"
    ]
    
    for i, model in enumerate(models, 1):
        print(f"{i}. {model}")
    
    # 选择模型
    try:
        choice = int(input("\n请选择要测试的模型 (输入序号): "))
        if 1 <= choice <= len(models):
            selected_model = models[choice - 1]
        else:
            print("无效选择，使用默认模型 grok-3")
            selected_model = "grok-3"
    except:
        print("无效输入，使用默认模型 grok-3")
        selected_model = "grok-3"
    
    # 获取用户输入
    user_message = input("\n请输入您想问的问题: ")
    
    # 发送聊天请求
    messages = [{"role": "user", "content": user_message}]
    chat_completion(selected_model, messages) 