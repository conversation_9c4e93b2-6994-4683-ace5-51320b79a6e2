# Vertical AI 客户端

这是一个用于与 Vertical AI 进行交互的客户端，支持从注册到使用模型的完整流程。

## 功能特点

- 用户注册与验证
- 用户资料设置
- 获取模型列表
- 创建新聊天
- 发送消息并获取响应（支持流式响应）
- 交互式聊天界面

## 安装依赖

```bash
pip install requests
```

## 使用方法

### 1. 运行交互式客户端

```bash
python vertical_complete_client.py
```

### 2. 认证方式

客户端提供两种认证方式：

- **注册新账户**：提供邮箱和密码，然后验证邮箱并完成资料设置
- **手动设置认证令牌**：直接提供已有的认证令牌

### 3. 聊天命令

在聊天界面中，可以使用以下命令：

- `/models` - 显示可用模型
- `/switch <model_id>` - 切换模型
- `/new` - 开始新对话
- `/quit` - 退出

### 4. 在代码中使用

```python
from vertical_complete_client import VerticalClient

# 创建客户端
client = VerticalClient()

# 设置认证令牌
client.set_auth_token("your-auth-token")

# 发送消息
response = client.chat("你好，请介绍一下自己", model_id="claude-4-sonnet-20250514", new_chat=True)
print(response["response"])
```

## 认证令牌说明

认证令牌格式为 `sb-ppdjlmajmpcqpkdmnzfd-auth-token`，可以通过以下方式获取：

1. 注册并验证邮箱
2. 从验证链接的重定向中提取 cookie
3. 保存并使用该令牌进行后续请求

## 模型支持

目前支持以下模型：

- claude-4-opus-20250514
- claude-4-sonnet-20250514

## 注意事项

- 认证令牌会保存在 `vertical_auth_token.txt` 文件中
- 验证链接只能使用一次，请确保保存认证令牌
- 如果遇到错误，请尝试重新获取聊天 ID 